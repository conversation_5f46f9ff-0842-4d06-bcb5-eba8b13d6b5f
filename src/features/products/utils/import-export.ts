import { read, utils, write } from 'xlsx';
import { saveAs } from 'file-saver';
import <PERSON> from 'papaparse';
import { TyreProduct, BrakeProduct } from '../types/product.types';
import { extractArticleNumbers, validateArticleNumber } from '@/services/tecdocService';

/**
 * Memory monitoring and management utilities
 */
export const getMemoryUsage = () => {
  if (performance.memory) {
    return {
      used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
      total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
      limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
    };
  }
  return null;
};

export const checkMemoryPressure = (dataSize: number): boolean => {
  const memory = getMemoryUsage();
  if (!memory) return false;

  // Consider memory pressure if we're using more than 80% of available memory
  // or if the dataset is very large relative to available memory
  const memoryPressure = memory.used / memory.limit > 0.8;
  const largeDatasetsRelativeToMemory = dataSize > (memory.limit - memory.used) * 1024 * 1024 * 0.1;

  return memoryPressure || largeDatasetsRelativeToMemory;
};

export const performMemoryCleanup = () => {
  // Force garbage collection if available (Chrome DevTools)
  if (window.gc) {
    console.log('🧹 Performing manual garbage collection...');
    window.gc();
  }

  // Clear any large temporary variables
  if (global.gc) {
    global.gc();
  }
};

/**
 * Parse an Excel file with streaming support for large files and error recovery
 */
export const parseExcelFile = (
  file: File,
  onProgress?: (progress: number) => void,
  chunkSize: number = 10000
): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    // Check memory before starting
    const memoryBefore = getMemoryUsage();
    console.log('📊 Memory before Excel parsing:', memoryBefore);

    // Check if file size might cause memory issues
    const isLargeFile = file.size > 50 * 1024 * 1024; // 50MB
    const isVeryLargeFile = file.size > 100 * 1024 * 1024; // 100MB+

    if (isVeryLargeFile) {
      console.log('⚡ Very large Excel file detected (100MB+), using ultra-fast parsing...');
      chunkSize = Math.min(chunkSize, 500); // Ultra-small chunks for massive files
    } else if (isLargeFile) {
      console.log('⚠️ Large Excel file detected, using conservative parsing...');
      chunkSize = Math.min(chunkSize, 2000); // Smaller chunks for large files
    }

    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const data = e.target?.result;
        if (!data) {
          reject(new Error('Failed to read file'));
          return;
        }

        onProgress?.(25); // File read complete

        // Memory check after file read
        const memoryAfterRead = getMemoryUsage();
        if (memoryAfterRead && checkMemoryPressure(file.size)) {
          console.log('⚠️ Memory pressure detected, performing cleanup...');
          performMemoryCleanup();
        }

        const workbook = read(data, {
          type: 'binary',
          cellDates: true,
          cellNF: false,
          cellText: false,
          // Optimize for large files
          bookVBA: false,
          bookSheets: false,
          bookProps: false
        });

        onProgress?.(50); // Workbook parsed

        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // Get the range to estimate total rows
        const range = utils.decode_range(worksheet['!ref'] || 'A1:A1');
        const totalRows = range.e.r + 1;

        console.log(`📊 Excel file contains ${totalRows} rows`);
        onProgress?.(60); // Range calculated

        // Convert to JSON with headers - use streaming approach for large files
        const jsonData = utils.sheet_to_json(worksheet, {
          header: 1,
          raw: false,
          defval: '',
          // Optimize for memory
          blankrows: false
        });

        onProgress?.(80); // Data converted

        // Extract headers (first row)
        const headers = (jsonData[0] as string[]) || [];
        if (headers.length === 0) {
          throw new Error('No headers found in Excel file');
        }

        // Convert to array of objects with memory-efficient processing
        const result: any[] = [];
        const dataRows = jsonData.slice(1);

        // Adaptive chunk size based on memory and data size
        const adaptiveChunkSize = isVeryLargeFile ? Math.min(chunkSize, 500) :
                                 isLargeFile ? Math.min(chunkSize, 1000) : chunkSize;

        // Process in chunks to avoid memory issues
        for (let i = 0; i < dataRows.length; i += adaptiveChunkSize) {
          const chunk = dataRows.slice(i, i + adaptiveChunkSize);

          chunk.forEach((row: any) => {
            try {
              const obj: Record<string, any> = {};
              headers.forEach((header, index) => {
                if (header && header.trim()) {
                  obj[header] = row[index] || '';
                }
              });
              // Only add rows with at least one non-empty value
              if (Object.values(obj).some(val => val && String(val).trim())) {
                result.push(obj);
              }
            } catch (rowError) {
              console.warn(`⚠️ Error processing row ${i + chunk.indexOf(row)}:`, rowError);
              // Continue processing other rows
            }
          });

          // Update progress
          const progress = 80 + (20 * (i + adaptiveChunkSize)) / dataRows.length;
          onProgress?.(Math.min(100, progress));

          // Memory management for large files
          if (isLargeFile && i % (adaptiveChunkSize * 5) === 0) {
            const currentMemory = getMemoryUsage();
            if (currentMemory && checkMemoryPressure(file.size)) {
              console.log('🧹 Performing intermediate memory cleanup...');
              performMemoryCleanup();
            }
          }
        }

        onProgress?.(100); // Complete

        const memoryAfter = getMemoryUsage();
        console.log('📊 Memory after Excel parsing:', memoryAfter);
        console.log(`✅ Excel parsing completed: ${result.length} valid rows from ${totalRows} total rows`);

        resolve(result);
      } catch (error) {
        console.error('❌ Excel parsing error:', error);
        performMemoryCleanup(); // Cleanup on error
        reject(error);
      }
    };

    reader.onerror = () => {
      performMemoryCleanup();
      reject(new Error('Failed to read Excel file'));
    };

    reader.onabort = () => {
      performMemoryCleanup();
      reject(new Error('Excel file reading was aborted'));
    };

    reader.readAsBinaryString(file);
  });
};

/**
 * Parse a CSV file with streaming support for large files and enhanced error handling
 */
export const parseCsvFile = (
  file: File,
  onProgress?: (progress: number) => void,
  chunkSize: number = 10000
): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    // Check memory before starting
    const memoryBefore = getMemoryUsage();
    console.log('📊 Memory before CSV parsing:', memoryBefore);

    const results: any[] = [];
    let processedRows = 0;
    let errorCount = 0;
    let totalEstimatedRows = 0;
    const isLargeFile = file.size > 20 * 1024 * 1024; // 20MB
    const isVeryLargeFile = file.size > 100 * 1024 * 1024; // 100MB+

    if (isVeryLargeFile) {
      console.log('⚡ Very large CSV file detected (100MB+), using ultra-fast parsing...');
      chunkSize = Math.min(chunkSize, 500); // Ultra-small chunks for massive files
    } else if (isLargeFile) {
      console.log('⚠️ Large CSV file detected, using optimized parsing...');
      chunkSize = Math.min(chunkSize, 2000); // Smaller chunks for large files
    }

    // Estimate total rows for better progress tracking
    const estimateRows = () => {
      const avgBytesPerRow = file.size > 1024 ? 100 : 50; // Rough estimate
      totalEstimatedRows = Math.floor(file.size / avgBytesPerRow);
    };
    estimateRows();

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      worker: true, // Use web worker for better performance
      chunk: (chunk, parser) => {
        try {
          // Process chunk data with error handling
          const validRows: any[] = [];

          chunk.data.forEach((row: any, index: number) => {
            try {
              // Only include rows with at least one non-empty value
              if (Object.values(row).some(val => val && String(val).trim())) {
                validRows.push(row);
              }
            } catch (rowError) {
              errorCount++;
              console.warn(`⚠️ Error processing CSV row ${processedRows + index}:`, rowError);
              // Continue processing other rows
            }
          });

          results.push(...validRows);
          processedRows += chunk.data.length;

          // Update progress based on processed rows vs estimated total
          const progress = Math.min(95, (processedRows / Math.max(totalEstimatedRows, processedRows)) * 100);
          onProgress?.(progress);

          // Enhanced memory management for very large files
          const memoryThreshold = isVeryLargeFile ? chunkSize * 5 : chunkSize * 10;
          if (results.length > memoryThreshold) {
            const currentMemory = getMemoryUsage();
            if (currentMemory && checkMemoryPressure(file.size)) {
              console.log('🧹 Memory pressure detected, performing cleanup...');
              performMemoryCleanup();
            }

            parser.pause();
            // Shorter pause for very large files to maintain speed
            const pauseDuration = isVeryLargeFile ? 5 : 10;
            setTimeout(() => {
              parser.resume();
            }, pauseDuration);
          }

          // Log progress for large files
          if (isLargeFile && processedRows % 10000 === 0) {
            console.log(`📊 CSV Progress: ${processedRows} rows processed, ${validRows.length} valid rows in this chunk`);
          }

        } catch (error) {
          console.error('❌ Error processing CSV chunk:', error);
          parser.abort();
          performMemoryCleanup();
          reject(error);
        }
      },
      complete: () => {
        try {
          onProgress?.(100);

          const memoryAfter = getMemoryUsage();
          console.log('📊 Memory after CSV parsing:', memoryAfter);
          console.log(`✅ CSV parsing completed: ${results.length} valid rows from ${processedRows} total rows`);

          if (errorCount > 0) {
            console.warn(`⚠️ ${errorCount} rows had parsing errors and were skipped`);
          }

          if (results.length === 0) {
            reject(new Error('No valid data found in CSV file. Please check the file format.'));
            return;
          }

          resolve(results);
        } catch (error) {
          console.error('❌ Error completing CSV parsing:', error);
          performMemoryCleanup();
          reject(error);
        }
      },
      error: (error) => {
        console.error('❌ Papa Parse error:', error);
        performMemoryCleanup();
        reject(new Error(`CSV parsing failed: ${error.message || 'Unknown error'}`));
      },
      // Optimize for large files
      fastMode: true,
      dynamicTyping: false, // Keep as strings for consistency
      encoding: 'UTF-8',
      // Enhanced error handling
      skipEmptyLines: 'greedy',
      transformHeader: (header: string) => {
        // Clean up headers
        return header.trim().replace(/[^\w\s-]/g, '');
      }
    });
  });
};

/**
 * Export data to an Excel file
 */
export const exportToExcel = (data: any[], filename: string): void => {
  // Create a worksheet
  const worksheet = utils.json_to_sheet(data);

  // Create a workbook
  const workbook = utils.book_new();
  utils.book_append_sheet(workbook, worksheet, 'Products');

  // Generate Excel file
  const excelBuffer = write(workbook, { bookType: 'xlsx', type: 'array' });

  // Save file
  const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });
  saveAs(blob, `${filename}.xlsx`);
};

/**
 * Export data to a CSV file
 */
export const exportToCsv = (data: any[], filename: string): void => {
  const csv = Papa.unparse(data);
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  saveAs(blob, `${filename}.csv`);
};

/**
 * Generate a template for importing products
 */
export const generateProductTemplate = (categoryId: string): any[] => {
  // Create a template with headers based on the category
  const baseTemplate = {
    name: '',
    sku: '',
    partArticleNumber: '',
    category: categoryId,
    subcategory: '',
    descriptionAndSpecifications: '',
    manufacturer: '',
    supplierName: '',
    stockQuantity: 0,
    retailPrice: 0,
    status: 'draft',
  };

  // Add category-specific fields
  if (categoryId === 'tyres') {
    return [{
      ...baseTemplate,
      width: '',
      aspectRatio: '',
      rimDiameter: '',
      loadIndex: '',
      speedRating: '',
      season: '',
    }];
  } else if (categoryId === 'brakes') {
    return [{
      ...baseTemplate,
      // No additional fields needed for brakes as we use the structured description
      // for technical specifications
      shippingOrigin: '',
    }];
  }

  return [baseTemplate];
};

/**
 * Enhanced data processing for bulk import
 */
export interface ImportedDataAnalysis {
  totalRows: number;
  detectedArticleNumbers: string[];
  columnMapping: Record<string, string>;
  dataQuality: {
    completeRows: number;
    incompleteRows: number;
    duplicateArticleNumbers: string[];
  };
}

/**
 * Analyze imported data for Part Article Numbers and data quality
 */
export const analyzeImportedData = (data: any[]): ImportedDataAnalysis => {
  const articleNumbers = extractArticleNumbers(data);
  const duplicates = findDuplicateArticleNumbers(data);

  // Analyze column mapping
  const columnMapping = detectColumnMapping(data);

  // Analyze data quality
  let completeRows = 0;
  let incompleteRows = 0;

  data.forEach(item => {
    const hasRequiredFields = item.name || item.productName || item.title;
    if (hasRequiredFields) {
      completeRows++;
    } else {
      incompleteRows++;
    }
  });

  return {
    totalRows: data.length,
    detectedArticleNumbers: articleNumbers,
    columnMapping,
    dataQuality: {
      completeRows,
      incompleteRows,
      duplicateArticleNumbers: duplicates
    }
  };
};

/**
 * Advanced fuzzy string matching for column names
 */
const calculateSimilarity = (str1: string, str2: string): number => {
  const s1 = str1.toLowerCase().trim();
  const s2 = str2.toLowerCase().trim();

  // Exact match
  if (s1 === s2) return 1.0;

  // Contains match
  if (s1.includes(s2) || s2.includes(s1)) return 0.8;

  // Levenshtein distance for fuzzy matching
  const matrix = Array(s2.length + 1).fill(null).map(() => Array(s1.length + 1).fill(null));

  for (let i = 0; i <= s1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= s2.length; j++) matrix[j][0] = j;

  for (let j = 1; j <= s2.length; j++) {
    for (let i = 1; i <= s1.length; i++) {
      const indicator = s1[i - 1] === s2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,     // deletion
        matrix[j - 1][i] + 1,     // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }

  const distance = matrix[s2.length][s1.length];
  const maxLength = Math.max(s1.length, s2.length);
  return maxLength === 0 ? 1 : 1 - (distance / maxLength);
};

/**
 * Comprehensive column mapping dictionary with multilingual support
 */
const COMPREHENSIVE_COLUMN_MAPPINGS = {
  // Part Article Number - Primary identifier
  partArticleNumber: [
    // English variations
    'part article number', 'partarticlenumber', 'part_article_number', 'part-article-number',
    'article number', 'articlenumber', 'article_number', 'article-number',
    'part number', 'partnumber', 'part_number', 'part-number',
    'sku', 'product code', 'productcode', 'product_code', 'product-code',
    'item code', 'itemcode', 'item_code', 'item-code',
    'reference', 'ref', 'reference number', 'referencenumber',
    'catalog number', 'catalognumber', 'catalog_number',
    'model number', 'modelnumber', 'model_number',

    // French variations
    'numéro d\'article', 'numero d\'article', 'numéro article', 'numero article',
    'référence', 'reference', 'réf', 'ref',
    'code produit', 'codeproduit', 'code_produit',
    'numéro de pièce', 'numero de piece', 'numéro pièce',
    'code article', 'codearticle', 'code_article',

    // Arabic variations
    'رقم القطعة', 'رقم المقال', 'كود المنتج', 'رقم المرجع', 'المرجع'
  ],

  // Product Name
  name: [
    // English variations
    'name', 'product name', 'productname', 'product_name', 'product-name',
    'title', 'item name', 'itemname', 'item_name', 'item-name',
    'description', 'product description', 'productdescription',
    'label', 'product title', 'producttitle',

    // French variations
    'nom', 'nom du produit', 'nom produit', 'titre',
    'désignation', 'designation', 'libellé', 'libelle',
    'nom de l\'article', 'nom article',

    // Arabic variations
    'الاسم', 'اسم المنتج', 'اسم القطعة', 'العنوان', 'الوصف'
  ],

  // Manufacturer/Brand
  manufacturer: [
    // English variations
    'manufacturer', 'brand', 'make', 'maker', 'supplier', 'vendor',
    'company', 'producer', 'fabricant', 'brand name', 'brandname',
    'manufacturer name', 'manufacturername',

    // French variations
    'fabricant', 'marque', 'constructeur', 'fournisseur',
    'nom de marque', 'nom marque', 'société',

    // Arabic variations
    'الصانع', 'الماركة', 'العلامة التجارية', 'المورد', 'الشركة المصنعة'
  ],

  // Stock Quantity
  stockQuantity: [
    // English variations
    'stock', 'stock quantity', 'stockquantity', 'stock_quantity',
    'quantity', 'qty', 'inventory', 'available', 'in stock',
    'stock level', 'stocklevel', 'stock_level',
    'units available', 'unitsavailable', 'units_available',
    'on hand', 'onhand', 'on_hand',

    // French variations
    'stock', 'quantité', 'quantite', 'qté', 'inventaire',
    'disponible', 'en stock', 'niveau stock',
    'quantité disponible', 'quantite disponible',

    // Arabic variations
    'المخزون', 'الكمية', 'الكمية المتاحة', 'المتوفر'
  ],

  // Retail Price
  retailPrice: [
    // English variations
    'price', 'retail price', 'retailprice', 'retail_price',
    'unit price', 'unitprice', 'unit_price',
    'selling price', 'sellingprice', 'selling_price',
    'list price', 'listprice', 'list_price',
    'cost', 'amount', 'value',

    // French variations
    'prix', 'prix de vente', 'prix vente', 'prix unitaire',
    'tarif', 'montant', 'coût', 'cout', 'valeur',

    // Arabic variations
    'السعر', 'سعر البيع', 'السعر بالتجزئة', 'التكلفة', 'القيمة'
  ],

  // Wholesale Price
  wholesalePrice: [
    // English variations
    'wholesale price', 'wholesaleprice', 'wholesale_price',
    'bulk price', 'bulkprice', 'bulk_price',
    'trade price', 'tradeprice', 'trade_price',
    'dealer price', 'dealerprice', 'dealer_price',
    'distributor price', 'distributorprice', 'distributor_price',

    // French variations
    'prix de gros', 'prix gros', 'prix grossiste',
    'prix distributeur', 'tarif grossiste',

    // Arabic variations
    'سعر الجملة', 'سعر التوزيع', 'سعر المورد'
  ],

  // Category
  category: [
    // English variations
    'category', 'type', 'product type', 'producttype', 'product_type',
    'classification', 'class', 'group', 'family',

    // French variations
    'catégorie', 'categorie', 'type', 'classification',
    'famille', 'groupe', 'classe',

    // Arabic variations
    'الفئة', 'النوع', 'التصنيف', 'المجموعة'
  ],

  // Subcategory
  subcategory: [
    // English variations
    'subcategory', 'sub category', 'sub-category', 'sub_category',
    'subtype', 'sub type', 'sub-type', 'sub_type',
    'subclass', 'sub class', 'sub-class', 'sub_class',

    // French variations
    'sous-catégorie', 'sous categorie', 'sous catégorie',
    'sous-type', 'sous type', 'sous-classe',

    // Arabic variations
    'الفئة الفرعية', 'النوع الفرعي', 'التصنيف الفرعي'
  ],

  // Description and Specifications
  descriptionAndSpecifications: [
    // English variations
    'description', 'specifications', 'specs', 'details',
    'notes', 'comments', 'remarks', 'features',
    'technical specifications', 'tech specs', 'techspecs',
    'product details', 'productdetails', 'product_details',

    // French variations
    'description', 'spécifications', 'specifications',
    'détails', 'details', 'caractéristiques', 'caracteristiques',
    'notes', 'commentaires', 'remarques',

    // Arabic variations
    'الوصف', 'المواصفات', 'التفاصيل', 'الخصائص', 'الملاحظات'
  ],

  // SKU (Secondary identifier)
  sku: [
    // English variations
    'sku', 'stock keeping unit', 'stockkeepingunit', 'stock_keeping_unit',
    'item sku', 'itemsku', 'item_sku',

    // French variations
    'sku', 'unité de gestion des stocks', 'référence interne',

    // Arabic variations
    'وحدة إدارة المخزون', 'الرمز التعريفي'
  ],

  // Primary Image
  primaryImage: [
    // English variations
    'image', 'primary image', 'primaryimage', 'primary_image',
    'main image', 'mainimage', 'main_image',
    'photo', 'picture', 'thumbnail',
    'product image', 'productimage', 'product_image',

    // French variations
    'image', 'image principale', 'photo', 'photo principale',
    'image produit', 'illustration',

    // Arabic variations
    'الصورة', 'الصورة الرئيسية', 'صورة المنتج'
  ],

  // Additional Images
  additionalImages: [
    // English variations
    'additional images', 'additionalimages', 'additional_images',
    'extra images', 'extraimages', 'extra_images',
    'more images', 'moreimages', 'more_images',
    'gallery', 'image gallery', 'imagegallery',

    // French variations
    'images supplémentaires', 'images additionnelles',
    'galerie', 'galerie d\'images',

    // Arabic variations
    'صور إضافية', 'معرض الصور', 'صور أخرى'
  ],

  // Supplier Name
  supplierName: [
    // English variations
    'supplier', 'supplier name', 'suppliername', 'supplier_name',
    'vendor', 'vendor name', 'vendorname', 'vendor_name',
    'distributor', 'distributor name',

    // French variations
    'fournisseur', 'nom fournisseur', 'distributeur',
    'nom du fournisseur',

    // Arabic variations
    'المورد', 'اسم المورد', 'الموزع'
  ],

  // Shipping Origin
  shippingOrigin: [
    // English variations
    'shipping origin', 'shippingorigin', 'shipping_origin',
    'origin', 'location', 'warehouse', 'source',
    'ship from', 'shipfrom', 'ship_from',

    // French variations
    'origine expédition', 'lieu d\'expédition', 'entrepôt',
    'provenance', 'localisation',

    // Arabic variations
    'مصدر الشحن', 'مكان الشحن', 'المستودع', 'المنشأ'
  ],

  // Status
  status: [
    // English variations
    'status', 'state', 'condition', 'availability',
    'product status', 'productstatus', 'product_status',

    // French variations
    'statut', 'état', 'etat', 'condition', 'disponibilité',

    // Arabic variations
    'الحالة', 'الوضع', 'التوفر'
  ]
};

/**
 * Advanced column mapping with fuzzy matching and multilingual support
 */
export const detectColumnMapping = (data: any[]): Record<string, string> => {
  if (data.length === 0) return {};

  const firstRow = data[0];
  const mapping: Record<string, string> = {};
  const unmappedColumns: string[] = [];

  // For each column in the imported data
  Object.keys(firstRow).forEach(column => {
    const cleanColumn = column.toLowerCase().trim();
    let bestMatch = '';
    let bestScore = 0;

    // Check against all possible field mappings
    for (const [targetField, variations] of Object.entries(COMPREHENSIVE_COLUMN_MAPPINGS)) {
      for (const variation of variations) {
        const score = calculateSimilarity(cleanColumn, variation);

        // Use a threshold of 0.6 for fuzzy matching
        if (score > bestScore && score >= 0.6) {
          bestScore = score;
          bestMatch = targetField;
        }
      }
    }

    if (bestMatch && bestScore >= 0.6) {
      mapping[column] = bestMatch;
    } else {
      unmappedColumns.push(column);
    }
  });

  // Store unmapped columns for later processing
  if (unmappedColumns.length > 0) {
    mapping['_unmappedColumns'] = unmappedColumns.join('|');
  }

  return mapping;
};

/**
 * Find duplicate article numbers in imported data
 */
export const findDuplicateArticleNumbers = (data: any[]): string[] => {
  const articleNumbers: string[] = [];
  const seen = new Set<string>();
  const duplicates = new Set<string>();

  data.forEach(item => {
    const possibleFields = [
      'partArticleNumber', 'part_article_number', 'articleNumber', 'article_number',
      'partNumber', 'part_number', 'sku', 'SKU', 'productCode', 'product_code'
    ];

    for (const field of possibleFields) {
      const value = item[field];
      if (value && validateArticleNumber(value)) {
        const articleNumber = value.toString().trim();
        if (seen.has(articleNumber)) {
          duplicates.add(articleNumber);
        } else {
          seen.add(articleNumber);
        }
        break;
      }
    }
  });

  return Array.from(duplicates);
};

/**
 * Enhanced mapping using AI-powered column analysis with retail focus and 100% accuracy
 */
export const mapImportedDataToProductsWithAI = async (
  data: any[],
  aiMappingResult: any,
  categoryId?: string,
  onProgress?: (progress: number, processed: number, total: number) => void,
  chunkSize: number = 5000
): Promise<Partial<TyreProduct | BrakeProduct>[]> => {
  // Enhanced AI result validation
  const aiDetectedCategory = aiMappingResult.suggestedCategory?.toLowerCase();
  const finalCategoryId = aiDetectedCategory || categoryId || 'brakes';

  console.log(`🔧 ENHANCED AI MAPPING:
  - AI detected category: "${aiDetectedCategory}"
  - Final category: "${finalCategoryId}"
  - Retail focus score: ${aiMappingResult.retailFocusScore || 0}%
  - High-confidence mappings: ${aiMappingResult.mappings?.length || 0}
  - Excluded B2B columns: ${aiMappingResult.excludedColumns?.length || 0}
  - Consolidated columns: ${aiMappingResult.consolidatedColumns?.length || 0}`);

  // Create enhanced mapping dictionary from validated AI results
  const aiMapping: Record<string, string> = {};
  const retailMappings = aiMappingResult.mappings || [];

  retailMappings.forEach((mapping: any) => {
    // Only use high-confidence mappings (already filtered by backend)
    if (mapping.confidence >= 95) {
      aiMapping[mapping.originalColumn] = mapping.targetField;
    }
  });

  console.log(`📊 Retail-focused mapping dictionary:`, aiMapping);

  // Process data in chunks for large datasets
  const results: Partial<TyreProduct | BrakeProduct>[] = [];
  const totalRows = data.length;

  for (let i = 0; i < totalRows; i += chunkSize) {
    const chunk = data.slice(i, i + chunkSize);
    const chunkResults = chunk.map((item, chunkIndex) => {
      const globalIndex = i + chunkIndex;
      // Helper function to get value using AI mapping
      const getAIValue = (targetField: string): any => {
        for (const [originalColumn, mappedField] of Object.entries(aiMapping)) {
          if (mappedField === targetField && item[originalColumn] !== undefined && item[originalColumn] !== null && item[originalColumn] !== '') {
            return item[originalColumn];
          }
        }
        return null;
      };

    // Process unmapped columns for zero data loss
    const processUnmappedData = (): string => {
      const unmappedData: string[] = [];
      const mappedColumns = Object.keys(aiMapping);

      Object.keys(item).forEach(column => {
        if (!mappedColumns.includes(column)) {
          const value = item[column];
          if (value !== undefined && value !== null && value !== '') {
            unmappedData.push(`${column}: ${value}`);
          }
        }
      });

      return unmappedData.length > 0 ? `\n\nAdditional Data:\n${unmappedData.join('\n')}` : '';
    };

    // Extract Part Article Number with AI mapping
    const partArticleNumber = getAIValue('partArticleNumber') || extractPartArticleNumber(item);

    // Generate intelligent product name using AI mapping
    const intelligentName = (() => {
      const brand = getAIValue('manufacturer') || getAIValue('supplierName');
      const type = getAIValue('name') || getAIValue('category');
      const vehicle = getAIValue('vehicleCompatibility');

      let name = '';
      if (brand) name += brand + ' ';
      if (type) name += type;
      if (vehicle) name += ` for ${vehicle}`;
      if (!name && partArticleNumber) name = `Part ${partArticleNumber}`;
      if (!name) name = `Product ${globalIndex + 1}`;

      return name.trim();
    })();

    // Generate enhanced description using intelligent data consolidation
    const enhancedDescription = (() => {
      // Use the enhanced description generation from AI service
      const { generateEnhancedDescription } = require('@/services/aiColumnMappingService');

      try {
        return generateEnhancedDescription(
          item,
          retailMappings,
          aiMappingResult.unmappedColumns || [],
          aiMappingResult.consolidatedColumns || []
        );
      } catch (error) {
        console.warn('Failed to generate enhanced description, using fallback:', error);

        // Fallback to basic description generation
        const sections: string[] = [];

        // Add base description if available
        const baseDesc = getAIValue('descriptionAndSpecifications');
        if (baseDesc) sections.push(baseDesc);

        // Add specifications from mapped fields
        const specs: string[] = [];
        const weight = getAIValue('weight');
        const dimensions = getAIValue('dimensions');
        const deliveryTime = getAIValue('deliveryTime');
        const oeNumbers = getAIValue('oeNumbers');

        if (weight) specs.push(`Weight: ${weight}`);
        if (dimensions) specs.push(`Dimensions: ${dimensions}`);
        if (deliveryTime) specs.push(`Delivery Time: ${deliveryTime}`);
        if (oeNumbers) specs.push(`OE Numbers: ${oeNumbers}`);

        if (specs.length > 0) {
          sections.push('Specifications:\n' + specs.join('\n'));
        }

        // Add retail-focused unmapped data (exclude B2B columns)
        const retailUnmappedData = processRetailUnmappedData();
        if (retailUnmappedData) sections.push(retailUnmappedData);

        return sections.join('\n\n');
      }
    })();

    // Enhanced retail-focused unmapped data processing
    const processRetailUnmappedData = (): string => {
      const retailData: string[] = [];
      const mappedColumns = Object.keys(aiMapping);
      const excludedColumns = aiMappingResult.excludedColumns || [];

      Object.keys(item).forEach(column => {
        // Skip if already mapped or excluded as B2B
        if (!mappedColumns.includes(column) && !excludedColumns.includes(column)) {
          const value = item[column];
          if (value !== undefined && value !== null && value !== '') {
            // Only include retail-relevant data
            const columnLower = column.toLowerCase();
            const isRetailRelevant = !columnLower.includes('wholesale') &&
                                   !columnLower.includes('supplier') &&
                                   !columnLower.includes('internal') &&
                                   !columnLower.includes('vendor');

            if (isRetailRelevant) {
              retailData.push(`${column.replace(/[_-]/g, ' ')}: ${value}`);
            }
          }
        }
      });

      return retailData.length > 0 ? `\n\nAdditional Product Information:\n${retailData.join('\n')}` : '';
    };

    const baseProduct: Partial<TyreProduct | BrakeProduct> = {
      // Core identification with intelligent naming
      name: intelligentName,
      sku: getAIValue('sku') || partArticleNumber || `SKU-${globalIndex + 1}`,
      partArticleNumber,

      // Categorization (simplified - subcategory is now optional)
      category: finalCategoryId,
      subcategory: '', // Subcategory is now optional and left empty
      descriptionAndSpecifications: enhancedDescription,

      // Images
      primaryImage: getAIValue('primaryImage') || '',
      additionalImages: [],

      // Business information
      manufacturer: getAIValue('manufacturer') || '',
      supplierName: getAIValue('supplierName') || '',
      stockQuantity: parseNumber(getAIValue('stockQuantity')) || 0,
      retailPrice: parseNumber(getAIValue('retailPrice')) || 0,

      // Vehicle compatibility (now text input)
      vehicleCompatibility: getAIValue('vehicleCompatibility') || '',

      // Shipping and logistics
      shippingOrigin: getAIValue('shippingOrigin') || '',

      // System fields
      status: getAIValue('status') || 'draft',
      createdAt: new Date(),
      updatedAt: new Date(),

      // Import metadata
      importRowIndex: globalIndex,
      hasPartArticleNumber: !!partArticleNumber,
      needsDataFetch: !partArticleNumber || !getAIValue('name'),
      aiMappingUsed: true,
      aiConfidence: aiMappingResult.confidence
    };

    // Add category-specific fields
    if (finalCategoryId === 'tyres') {
      return {
        ...baseProduct,
        width: parseNumber(getAIValue('width')) || undefined,
        aspectRatio: parseNumber(getAIValue('aspectRatio')) || undefined,
        rimDiameter: parseNumber(getAIValue('rimDiameter')) || undefined,
        loadIndex: parseNumber(getAIValue('loadIndex')) || undefined,
        speedRating: getAIValue('speedRating') || undefined,
        season: getAIValue('season') || undefined,
      };
    }

    return baseProduct;
    });

    // Add chunk results to main results
    results.push(...chunkResults);

    // Update progress
    const processed = Math.min(i + chunkSize, totalRows);
    const progress = (processed / totalRows) * 100;
    onProgress?.(progress, processed, totalRows);

    // Allow UI to update between chunks for large datasets
    if (totalRows > 10000 && i + chunkSize < totalRows) {
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    console.log(`📊 Processed chunk ${Math.floor(i / chunkSize) + 1}/${Math.ceil(totalRows / chunkSize)} (${processed}/${totalRows} rows)`);
  }

  console.log(`✅ AI mapping completed: ${results.length} products processed`);
  return results;
};

/**
 * Enhanced mapping of imported data to product objects with zero data loss (Legacy)
 */
export const mapImportedDataToProducts = (data: any[], categoryId: string): Partial<TyreProduct | BrakeProduct>[] => {
  const columnMapping = detectColumnMapping(data);

  return data.map((item, index) => {
    // Extract Part Article Number using multiple strategies
    const partArticleNumber = extractPartArticleNumber(item);

    // Process unmapped columns to ensure zero data loss
    const processUnmappedData = (item: any, mapping: Record<string, string>): string => {
      const unmappedData: string[] = [];
      const unmappedColumns = mapping['_unmappedColumns']?.split('|') || [];

      // Add unmapped columns to description
      unmappedColumns.forEach(column => {
        const value = item[column];
        if (value !== undefined && value !== null && value !== '') {
          unmappedData.push(`${column}: ${value}`);
        }
      });

      // Also check for any columns not in mapping at all
      Object.keys(item).forEach(column => {
        const isMapped = Object.keys(mapping).some(mappedCol =>
          mappedCol === column || mapping[mappedCol] === column
        );

        if (!isMapped && column !== '_unmappedColumns') {
          const value = item[column];
          if (value !== undefined && value !== null && value !== '') {
            unmappedData.push(`${column}: ${value}`);
          }
        }
      });

      return unmappedData.length > 0 ? `\n\nAdditional Data:\n${unmappedData.join('\n')}` : '';
    };

    // Get base description and append unmapped data
    const baseDescription = getValueFromMapping(item, columnMapping, 'descriptionAndSpecifications') || '';
    const unmappedDataString = processUnmappedData(item, columnMapping);
    const finalDescription = baseDescription + unmappedDataString;

    const baseProduct: Partial<TyreProduct | BrakeProduct> = {
      // Core identification
      name: getValueFromMapping(item, columnMapping, 'name') || `Product ${index + 1}`,
      sku: getValueFromMapping(item, columnMapping, 'sku') || item.sku || item.SKU || partArticleNumber || `SKU-${index + 1}`,
      partArticleNumber: partArticleNumber,

      // Categorization
      category: categoryId,
      subcategory: getValueFromMapping(item, columnMapping, 'subcategory') || '',
      descriptionAndSpecifications: finalDescription,

      // Images
      primaryImage: getValueFromMapping(item, columnMapping, 'primaryImage') || '',
      additionalImages: [],

      // Business information
      manufacturer: getValueFromMapping(item, columnMapping, 'manufacturer') || '',
      supplierName: getValueFromMapping(item, columnMapping, 'supplierName') || item.supplierName || item.supplier_name || '',
      stockQuantity: parseNumber(getValueFromMapping(item, columnMapping, 'stockQuantity')) || 0,
      retailPrice: parseNumber(getValueFromMapping(item, columnMapping, 'retailPrice')) || 0,

      // Shipping and logistics
      shippingOrigin: getValueFromMapping(item, columnMapping, 'shippingOrigin') || '',

      // System fields
      status: getValueFromMapping(item, columnMapping, 'status') || item.status || 'draft',
      createdAt: new Date(),
      updatedAt: new Date(),

      // Import metadata
      importRowIndex: index,
      hasPartArticleNumber: !!partArticleNumber,
      needsDataFetch: !partArticleNumber || !getValueFromMapping(item, columnMapping, 'name')
    };

    // Add category-specific fields
    if (categoryId === 'tyres') {
      return {
        ...baseProduct,
        width: parseNumber(getValueFromMapping(item, columnMapping, 'width') || item.width) || undefined,
        aspectRatio: parseNumber(getValueFromMapping(item, columnMapping, 'aspectRatio') || item.aspectRatio || item.aspect_ratio) || undefined,
        rimDiameter: parseNumber(getValueFromMapping(item, columnMapping, 'rimDiameter') || item.rimDiameter || item.rim_diameter) || undefined,
        loadIndex: parseNumber(getValueFromMapping(item, columnMapping, 'loadIndex') || item.loadIndex || item.load_index) || undefined,
        speedRating: getValueFromMapping(item, columnMapping, 'speedRating') || item.speedRating || item.speed_rating || undefined,
        season: getValueFromMapping(item, columnMapping, 'season') || item.season || undefined,
      };
    } else if (categoryId === 'brakes') {
      return {
        ...baseProduct,
        // Vehicle compatibility would be handled separately
      };
    }

    return baseProduct;
  });
};

/**
 * Extract Part Article Number from imported item using multiple strategies
 */
export const extractPartArticleNumber = (item: any): string | undefined => {
  const possibleFields = [
    'partArticleNumber', 'part_article_number', 'articleNumber', 'article_number',
    'partNumber', 'part_number', 'sku', 'SKU', 'productCode', 'product_code'
  ];

  for (const field of possibleFields) {
    const value = item[field];
    if (value && validateArticleNumber(value)) {
      return value.toString().trim();
    }
  }

  return undefined;
};

/**
 * Get value from item using enhanced column mapping
 */
export const getValueFromMapping = (item: any, mapping: Record<string, string>, targetField: string): any => {
  // First try direct field access
  if (item[targetField] !== undefined && item[targetField] !== null && item[targetField] !== '') {
    return item[targetField];
  }

  // Then try mapped columns
  for (const [column, mappedField] of Object.entries(mapping)) {
    if (mappedField === targetField && item[column] !== undefined && item[column] !== null && item[column] !== '') {
      return item[column];
    }
  }

  return undefined;
};

/**
 * Parse number with fallback
 */
export const parseNumber = (value: any): number | undefined => {
  if (value === null || value === undefined || value === '') {
    return undefined;
  }

  const num = Number(value);
  return isNaN(num) ? undefined : num;
};

/**
 * Enhanced product validation with Part Article Number focus
 */
export const validateImportedProducts = (products: Partial<TyreProduct | BrakeProduct>[]): {
  valid: Partial<TyreProduct | BrakeProduct>[];
  invalid: { product: Partial<TyreProduct | BrakeProduct>; errors: string[] }[];
  needsDataFetch: Partial<TyreProduct | BrakeProduct>[];
} => {
  const valid: Partial<TyreProduct | BrakeProduct>[] = [];
  const invalid: { product: Partial<TyreProduct | BrakeProduct>; errors: string[] }[] = [];
  const needsDataFetch: Partial<TyreProduct | BrakeProduct>[] = [];

  products.forEach(product => {
    const errors: string[] = [];

    // Basic validation
    if (!product.name || product.name.trim().length === 0) {
      errors.push('Product name is required');
    }

    if (!product.manufacturer || product.manufacturer.trim().length === 0) {
      errors.push('Manufacturer is required');
    }

    if (product.stockQuantity === undefined || product.stockQuantity < 0) {
      errors.push('Valid stock quantity is required');
    }

    // Part Article Number validation
    if (product.partArticleNumber && !validateArticleNumber(product.partArticleNumber)) {
      errors.push('Invalid Part Article Number format');
    }

    if (errors.length === 0) {
      valid.push(product);

      // Check if product needs data fetching
      if (product.partArticleNumber && (!product.name || !product.descriptionAndSpecifications)) {
        needsDataFetch.push(product);
      }
    } else {
      invalid.push({ product, errors });
    }
  });

  return { valid, invalid, needsDataFetch };
};



/**
 * Prepare products for export
 */
export const prepareProductsForExport = (products: (TyreProduct | BrakeProduct)[]): any[] => {
  return products.map(product => {
    const baseProduct = {
      id: product.id,
      name: product.name,
      sku: product.sku,
      barcode: product.barcode || '',
      category: product.category,
      subcategory: product.subcategory || '',
      description: product.description || '',
      manufacturer: product.manufacturer,
      supplierName: product.supplierName || '',
      stockQuantity: product.stockQuantity,
      retailPrice: product.retailPrice || 0,
      status: product.status,
      shippingOrigin: product.shippingOrigin || '',
    };

    // Add category-specific fields
    if (product.category === 'tyres') {
      const tyreProduct = product as TyreProduct;
      return {
        ...baseProduct,
        width: tyreProduct.width || '',
        aspectRatio: tyreProduct.aspectRatio || '',
        rimDiameter: tyreProduct.rimDiameter || '',
        loadIndex: tyreProduct.loadIndex || '',
        speedRating: tyreProduct.speedRating || '',
        season: tyreProduct.season || '',
      };
    } else if (product.category === 'brakes') {
      const brakeProduct = product as BrakeProduct;
      return {
        ...baseProduct,
        // No additional fields needed for brakes as we use the structured description
        // for technical specifications
      };
    }

    return baseProduct;
  });
};
