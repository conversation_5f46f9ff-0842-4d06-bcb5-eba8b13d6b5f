import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { TyreProduct } from '../types/product.types';
import {
  parseExcelFile,
  parseCsvFile,
  mapImportedDataToProducts,
  mapImportedDataToProductsWithAI,
  validateImportedProducts,
  generateProductTemplate,
  exportToExcel,
  analyzeImportedData,
  ImportedDataAnalysis
} from '../utils/import-export';
import { validateImportFileUpload } from '@/config/security';
import {
  analyzeColumnsWithAI,
  fallbackColumnMapping,
  prepareDataForAI,
  AIColumnMappingResult
} from '@/services/aiColumnMappingService';
import { getAIMappingStatus } from '@/services/aiMappingBackend';
import { ImportReviewTable } from './ImportReviewTable';
import { batchSearchByArticleNumbers, extractArticleNumbers } from '@/services/tecdocService';
import { DataEnrichmentService, EnrichmentProgress, EnrichmentSession, ProductEnrichmentResult } from '@/services/dataEnrichmentService';
import { EnrichmentProgressStepper } from './EnrichmentProgressStepper';
import { AIMappingSummary } from './AIMappingSummary';
import { FinalReviewTable } from './FinalReviewTable';
import { toast } from 'sonner';
import { FileSpreadsheet, Upload, Download, AlertTriangle, CheckCircle2, X, FileUp, Loader2, BarChart3, Sparkles, Search } from 'lucide-react';
import { EnhancedImportDialog } from './EnhancedImportDialog';
import type { EnhancedProductData } from '@/services/enhancedImportService';

interface ImportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (products: Partial<TyreProduct>[]) => Promise<void>;
  categoryId: string;
}

export const ImportDialog: React.FC<ImportDialogProps> = ({
  isOpen,
  onClose,
  onImport,
  categoryId,
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string>('upload');
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [importedData, setImportedData] = useState<any[]>([]);
  const [mappedProducts, setMappedProducts] = useState<Partial<TyreProduct>[]>([]);
  const [validationResults, setValidationResults] = useState<{
    valid: Partial<TyreProduct>[],
    invalid: { product: Partial<TyreProduct>, errors: string[] }[],
    needsDataFetch: Partial<TyreProduct>[]
  } | null>(null);
  const [dataAnalysis, setDataAnalysis] = useState<ImportedDataAnalysis | null>(null);
  const [isImporting, setIsImporting] = useState<boolean>(false);
  const [importProgress, setImportProgress] = useState<number>(0);
  const [isFetchingData, setIsFetchingData] = useState<boolean>(false);
  const [fetchProgress, setFetchProgress] = useState<{ completed: number; total: number }>({ completed: 0, total: 0 });

  // Enhanced enrichment states
  const [enrichmentService, setEnrichmentService] = useState<DataEnrichmentService | null>(null);
  const [enrichmentProgress, setEnrichmentProgress] = useState<EnrichmentProgress | null>(null);
  const [enrichmentSession, setEnrichmentSession] = useState<EnrichmentSession | null>(null);
  const [isEnriching, setIsEnriching] = useState<boolean>(false);

  // AI Web Search Import states
  const [showEnhancedImport, setShowEnhancedImport] = useState<boolean>(false);

  // AI Mapping states
  const [aiMappingResult, setAiMappingResult] = useState<AIColumnMappingResult | null>(null);
  const [isAnalyzingColumns, setIsAnalyzingColumns] = useState<boolean>(false);
  const [useAiMapping, setUseAiMapping] = useState<boolean>(true);
  const [aiServiceStatus, setAiServiceStatus] = useState<any>(null);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Check AI service status on mount
  useEffect(() => {
    const checkAIStatus = async () => {
      try {
        const status = getAIMappingStatus();
        setAiServiceStatus(status);

        // Enable AI mapping by default if service is available
        if (status.available) {
          setUseAiMapping(true);
        }
      } catch (error) {
        console.error('Failed to check AI service status:', error);
        setUseAiMapping(false);
      }
    };

    checkAIStatus();
  }, []);

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (!selectedFile) return;
    
    setFile(selectedFile);
    setImportedData([]);
    setMappedProducts([]);
    setValidationResults(null);
  };

  // Handle file upload and parsing
  const handleFileUpload = async () => {
    if (!file) {
      toast.error('Please select a file to upload');
      return;
    }

    // Validate file before processing
    const validation = validateImportFileUpload(file);
    if (!validation.valid) {
      toast.error(validation.error || 'Invalid file');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      console.log(`📁 Processing file: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`);

      // Parse file based on type with streaming support
      let data: any[] = [];
      const fileExtension = file.name.split('.').pop()?.toLowerCase();

      const onProgress = (progress: number) => {
        setUploadProgress(Math.min(90, progress));
      };

      // Optimize chunk size for very large files (66k+ rows)
      const isVeryLargeFile = file.size > 100 * 1024 * 1024; // 100MB+
      const optimizedChunkSize = isVeryLargeFile ? 1000 : 5000; // Much smaller chunks for massive files

      if (fileExtension === 'csv') {
        console.log('🔄 Parsing CSV file with high-performance streaming...');
        data = await parseCsvFile(file, onProgress, optimizedChunkSize);
      } else if (fileExtension === 'xlsx' || fileExtension === 'xls') {
        console.log('🔄 Parsing Excel file with high-performance streaming...');
        data = await parseExcelFile(file, onProgress, optimizedChunkSize);
      } else {
        throw new Error('Unsupported file format. Please upload a CSV or Excel file.');
      }

      setUploadProgress(100);
      console.log(`✅ File parsed successfully: ${data.length} rows`);
      
      // Memory management: Clear any previous data
      setMappedProducts([]);
      setDataAnalysis(null);
      setAiMappingResult(null);

      // Analyze imported data with memory-efficient processing
      console.log('🔍 Analyzing imported data...');
      const analysis = analyzeImportedData(data);
      setDataAnalysis(analysis);

      // Try AI-powered column mapping if enabled
      let products: Partial<TyreProduct>[] = [];
      let aiResult: AIColumnMappingResult | null = null;

      if (useAiMapping) {
        try {
          setIsAnalyzingColumns(true);
          console.log('🔍 Starting intelligent column analysis...');

          // Memory check before AI processing
          if (data.length > 50000) {
            console.log('⚠️ Large dataset detected, using optimized AI processing...');
          }

          // Use advanced analysis service with error recovery
          aiResult = await analyzeColumnsWithAI(data);
          console.log('✅ Column analysis completed:', aiResult);
          setAiMappingResult(aiResult);

          // Display clear mapping results to user
          if (aiResult && aiResult.mappings && aiResult.mappings.length > 0) {
            console.log('📋 MAPPING RESULTS:');
            aiResult.mappings.forEach(mapping => {
              console.log(`  "${mapping.originalColumn}" → "${mapping.targetField}" (${mapping.confidence}%)`);
            });
            console.log(`📂 Suggested Category: ${aiResult.suggestedCategory}`);

            // Show user-friendly mapping summary
            const mappingSummary = aiResult.mappings.map(m =>
              `"${m.originalColumn}" → "${m.targetField}"`
            ).join('\n');

            toast.success(`AI Mapping Complete!\n${mappingSummary}\nCategory: ${aiResult.suggestedCategory}`, {
              duration: 8000
            });
          }

          // Validate AI result before using it
          if (!aiResult || !aiResult.mappings || !Array.isArray(aiResult.mappings)) {
            throw new Error('Invalid mapping result structure');
          }

          // Use intelligent mapping to create products with progress tracking
          console.log('🔄 Creating products with intelligent mapping...');
          console.log(`🔧 CATEGORY FIX: System detected "${aiResult.suggestedCategory}", current categoryId: "${categoryId}"`);

          // Progress tracking for large datasets
          const onMappingProgress = (progress: number, processed: number, total: number) => {
            console.log(`📊 Mapping progress: ${progress.toFixed(1)}% (${processed}/${total})`);
            // Update progress bar if needed
            setUploadProgress(Math.min(95, 90 + (progress * 0.05)));
          };

          // Pass undefined as categoryId to let detected category take precedence
          // Ultra-optimized chunk sizes for massive datasets
          const mappingChunkSize = data.length > 50000 ? 500 :  // 50k+ rows: ultra-small chunks
                                  data.length > 20000 ? 1000 : // 20k+ rows: small chunks
                                  data.length > 10000 ? 2000 : // 10k+ rows: medium chunks
                                  5000; // Default chunk size

          products = await mapImportedDataToProductsWithAI(
            data,
            aiResult,
            undefined,
            onMappingProgress,
            mappingChunkSize
          );

          console.log('✅ Products created:', products.length);
          console.log('📂 First product category:', products[0]?.category);

          toast.success(
            `Smart mapping completed with ${aiResult.confidence}% confidence. Category: ${aiResult.suggestedCategory}. Processed ${products.length} products.`
          );
        } catch (error) {
          console.error('❌ Smart mapping failed:', error);
          console.error('Error details:', {
            message: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined,
            dataLength: data.length,
            headers: Object.keys(data[0] || {}),
            memoryUsage: performance.memory ? {
              used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
              total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
              limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            } : 'Not available'
          });

          // Memory cleanup on error
          if (data.length > 10000) {
            console.log('🧹 Performing memory cleanup after error...');
            // Force garbage collection if available
            if (window.gc) {
              window.gc();
            }
          }

          toast.error(`Smart mapping failed: ${error instanceof Error ? error.message : 'Unknown error'}. Using standard mapping.`);

          // Fallback to traditional mapping with error handling
          try {
            console.log('🔄 Using fallback mapping...');
            products = mapImportedDataToProducts(data, categoryId);
            console.log('✅ Fallback products created:', products.length);
          } catch (fallbackError) {
            console.error('❌ Fallback mapping also failed:', fallbackError);
            toast.error('Both smart and standard mapping failed. Please check your file format.');
            throw fallbackError;
          }
        } finally {
          setIsAnalyzingColumns(false);
        }
      } else {
        // Use traditional mapping with error handling
        try {
          console.log('🔄 Using traditional mapping...');
          products = mapImportedDataToProducts(data, categoryId);
          console.log('✅ Traditional mapping completed:', products.length);
        } catch (error) {
          console.error('❌ Traditional mapping failed:', error);
          toast.error('Product mapping failed. Please check your file format and try again.');
          throw error;
        }
      }

      // Validate products
      const validation = validateImportedProducts(products);

      setImportedData(data);
      setMappedProducts(products);
      setValidationResults(validation);

      // Move to review tab
      console.log('🔄 Moving to review tab with:', {
        importedDataLength: data.length,
        mappedProductsLength: products.length,
        validationResults: validation,
        aiMappingResult: aiResult ? 'Present' : 'Not present'
      });
      setActiveTab('review');

      const articleNumbersFound = analysis.detectedArticleNumbers.length;
      toast.success(
        t('products.fileUploadSuccess', { total: data.length, articleNumbers: articleNumbersFound })
      );
    } catch (error) {
      console.error('Error uploading file:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to upload file');
    } finally {
      setIsUploading(false);
    }
  };

  // Enhanced comprehensive data enrichment
  const handleComprehensiveEnrichment = async () => {
    if (!mappedProducts || mappedProducts.length === 0) {
      toast.error('No products available for enrichment');
      return;
    }

    setIsEnriching(true);

    try {
      console.log('🚀 Starting comprehensive data enrichment...');

      // Initialize enrichment service with progress callback
      const service = new DataEnrichmentService((progress) => {
        setEnrichmentProgress(progress);
      });

      setEnrichmentService(service);

      // Start enrichment process
      const session = await service.enrichProducts(mappedProducts);
      setEnrichmentSession(session);

      // Get enriched products
      const enrichedProducts = service.getEnrichedProducts();
      setMappedProducts(enrichedProducts);

      // Re-validate products
      const validation = validateImportedProducts(enrichedProducts);
      setValidationResults(validation);

      toast.success(
        `🎉 Data enrichment completed! ${session.successCount} products fully enriched, ${session.partialCount} partially enriched`
      );

      // Automatically move to final review
      setTimeout(() => {
        setActiveTab('final-review');
      }, 1000);

    } catch (error) {
      console.error('❌ Comprehensive enrichment failed:', error);
      toast.error('Data enrichment failed. Please try again.');
    } finally {
      setIsEnriching(false);
    }
  };

  // Legacy batch data fetching (kept for compatibility)
  const handleBatchFetchData = async () => {
    if (!dataAnalysis || dataAnalysis.detectedArticleNumbers.length === 0) {
      toast.error(t('products.noArticleNumbers'));
      return;
    }

    setIsFetchingData(true);
    setFetchProgress({ completed: 0, total: dataAnalysis.detectedArticleNumbers.length });

    try {
      const results = await batchSearchByArticleNumbers(
        dataAnalysis.detectedArticleNumbers,
        (completed, total) => {
          setFetchProgress({ completed, total });
        }
      );

      // Update products with fetched data
      const updatedProducts = mappedProducts.map(product => {
        if (product.partArticleNumber && results.has(product.partArticleNumber)) {
          const result = results.get(product.partArticleNumber);
          if (result?.success && result.data) {
            return {
              ...product,
              name: result.data.productName || product.name,
              manufacturer: result.data.brandName || product.manufacturer,
              descriptionAndSpecifications: result.data.description || product.descriptionAndSpecifications,
              dataFetchedFromTecDoc: true,
              tecDocSource: result.source,
              updatedAt: new Date()
            };
          }
        }
        return product;
      });

      setMappedProducts(updatedProducts);

      // Re-validate products
      const validation = validateImportedProducts(updatedProducts);
      setValidationResults(validation);

      const successCount = Array.from(results.values()).filter(r => r.success).length;
      toast.success(t('products.fetchDataSuccess', { count: successCount, total: dataAnalysis.detectedArticleNumbers.length }));

    } catch (error) {
      console.error('Error fetching batch data:', error);
      toast.error('Failed to fetch product data');
    } finally {
      setIsFetchingData(false);
    }
  };

  // Handle individual product update
  const handleProductUpdate = (index: number, updatedProduct: Partial<TyreProduct>) => {
    const newProducts = [...mappedProducts];
    newProducts[index] = updatedProduct;
    setMappedProducts(newProducts);

    // Re-validate products
    const validation = validateImportedProducts(newProducts);
    setValidationResults(validation);
  };

  // Handle enhanced import completion
  const handleEnhancedImportComplete = async (enhancedProducts: EnhancedProductData[]) => {
    try {
      // Convert enhanced products to TyreProduct format
      const convertedProducts: Partial<TyreProduct>[] = enhancedProducts.map(product => ({
        partArticleNumber: product.partArticleNumber,
        name: product.productName,
        manufacturer: product.manufacturer,
        category: product.category,
        stockQuantity: product.stockQuantity,
        retailPrice: product.retailPrice,
        vehicleCompatibility: product.vehicleCompatibility,
        description: `${product.description.generalInformation}\n\n${product.description.technicalInformation}\n\n${product.description.applicability}\n\n${product.description.originalNumbers}\n\n${product.description.oemNumbers}`,
        // Add other required fields with defaults
        status: 'active' as const,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }));

      await onImport(convertedProducts);
      onClose();
      toast.success(`Successfully imported ${enhancedProducts.length} AI-enhanced products!`);
    } catch (error) {
      console.error('Enhanced import failed:', error);
      toast.error('Failed to import enhanced products');
    }
  };

  // Handle import
  const handleImport = async () => {
    if (!validationResults || validationResults.valid.length === 0) {
      toast.error('No valid products to import');
      return;
    }

    setIsImporting(true);
    setImportProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setImportProgress(prev => {
          const newProgress = prev + 5;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 100);

      // Import valid products
      await onImport(validationResults.valid);

      clearInterval(progressInterval);
      setImportProgress(100);

      toast.success(t('products.importSuccess', { count: validationResults.valid.length }));

      // Reset state and close dialog
      setTimeout(() => {
        resetDialogState();
        onClose();
      }, 1000);
    } catch (error) {
      console.error('Error importing products:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to import products');
    } finally {
      setIsImporting(false);
    }
  };

  // Reset dialog state
  const resetDialogState = () => {
    setFile(null);
    setImportedData([]);
    setMappedProducts([]);
    setValidationResults(null);
    setDataAnalysis(null);
    setActiveTab('upload');
    setFetchProgress({ completed: 0, total: 0 });
    setAiMappingResult(null);
    setIsAnalyzingColumns(false);

    // Reset enrichment states
    setEnrichmentService(null);
    setEnrichmentProgress(null);
    setEnrichmentSession(null);
    setIsEnriching(false);
  };

  // Handle template download
  const handleDownloadTemplate = () => {
    try {
      const template = generateProductTemplate(categoryId);
      exportToExcel(template, `product_import_template_${categoryId}`);
      toast.success('Template downloaded successfully');
    } catch (error) {
      console.error('Error downloading template:', error);
      toast.error('Failed to download template');
    }
  };

  // Reset state when dialog closes
  const handleDialogClose = (open: boolean) => {
    if (!open) {
      resetDialogState();
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogClose}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="border-b pb-4">
          <DialogTitle className="flex items-center gap-3 text-xl font-semibold">
            <div className="p-2 bg-blue-100 rounded-lg">
              <FileUp className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <div className="text-gray-900">{t('products.importProducts')}</div>
              <div className="text-sm font-normal text-gray-600 mt-1">
                {t('products.importFromFile')}
              </div>
            </div>
          </DialogTitle>
        </DialogHeader>

        {/* Enhanced Import Option */}
        <div className="border-b pb-4 mb-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Import Method</h3>
              <p className="text-sm text-gray-600">Choose between standard import or AI-powered enhancement</p>
            </div>
            <Button
              onClick={() => setShowEnhancedImport(true)}
              className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              <Search className="w-4 h-4" />
              AI-Powered Import
            </Button>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 overflow-hidden flex flex-col">
          <TabsList className="grid w-full grid-cols-5 h-12 bg-gray-50">
            <TabsTrigger value="upload" disabled={isImporting || isFetchingData || isEnriching} className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
              <div className="flex items-center space-x-2">
                <Upload className="w-4 h-4" />
                <span className="text-sm font-medium">Upload</span>
              </div>
            </TabsTrigger>
            <TabsTrigger value="review" disabled={!importedData.length || isImporting || isFetchingData || isEnriching} className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
              <div className="flex items-center space-x-2">
                <BarChart3 className="w-4 h-4" />
                <span className="text-sm font-medium">Review</span>
              </div>
            </TabsTrigger>
            <TabsTrigger value="enrich" disabled={!mappedProducts.length || isImporting || isFetchingData} className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
              <div className="flex items-center space-x-2">
                <Sparkles className="w-4 h-4" />
                <span className="text-sm font-medium">Enrich</span>
              </div>
            </TabsTrigger>
            <TabsTrigger value="final-review" disabled={!enrichmentSession || enrichmentSession.progress.step !== 'complete' || isImporting} className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
              <div className="flex items-center space-x-2">
                <CheckCircle2 className="w-4 h-4" />
                <span className="text-sm font-medium">Final Review</span>
              </div>
            </TabsTrigger>
            <TabsTrigger value="complete" disabled={!validationResults?.valid.length || isImporting || isFetchingData || isEnriching} className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
              <div className="flex items-center space-x-2">
                <CheckCircle2 className="w-4 h-4" />
                <span className="text-sm font-medium">Import</span>
              </div>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="flex-1 overflow-auto p-6 space-y-6">
            <div className="space-y-6">
              <div className="border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors rounded-lg p-8 flex flex-col items-center justify-center gap-4 bg-gray-50/50 hover:bg-blue-50/50">
                <div className="p-3 bg-blue-100 rounded-full">
                  <FileSpreadsheet className="h-8 w-8 text-blue-600" />
                </div>
                <div className="text-center space-y-2">
                  <h3 className="text-lg font-semibold text-gray-900">{t('products.uploadYourProductFile')}</h3>
                  <p className="text-sm text-gray-600">
                    {t('products.dragAndDropOrClick')}
                  </p>
                  <p className="text-xs text-gray-500">
                    {t('products.supportedFormats')} • {t('products.maxFileSize')}
                  </p>
                </div>
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading}
                  className="bg-white hover:bg-gray-50"
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Browse Files
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv,.xlsx,.xls"
                  className="hidden"
                  onChange={handleFileChange}
                  disabled={isUploading}
                />
              </div>

              {file && (
                <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <FileSpreadsheet className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{file.name}</div>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs bg-gray-50">
                            {(file.size / 1024).toFixed(1)} KB
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {file.type || 'Unknown format'}
                          </span>
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setFile(null)}
                      disabled={isUploading}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  {isUploading && (
                    <div className="mt-3 space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                          <span className="text-sm font-medium text-gray-900">
                            {uploadProgress < 30 ? t('products.readingFile') :
                             uploadProgress < 60 ? t('products.analyzingStructure') :
                             uploadProgress < 90 ? t('products.processingData') :
                             t('products.finalizing')}
                          </span>
                        </div>
                        <span className="text-sm font-medium text-blue-600">{uploadProgress}%</span>
                      </div>
                      <Progress value={uploadProgress} className="h-2" />
                      <p className="text-xs text-gray-600">
                        {uploadProgress < 30 ? t('products.parsingFileContent') :
                         uploadProgress < 60 ? t('products.detectingColumns') :
                         uploadProgress < 90 ? t('products.mappingDataToFields') :
                         t('products.preparingDataForReview')}
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Intelligent Column Mapping Configuration */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-blue-900 flex items-center gap-2">
                      <BarChart3 className="w-4 h-4" />
                      {t('products.intelligentColumnMapping')}
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Enhanced
                      </span>
                    </h4>
                    <p className="text-sm text-blue-700">
                      {t('products.automaticallyAnalyze')}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="useAiMapping"
                      checked={useAiMapping}
                      onChange={(e) => setUseAiMapping(e.target.checked)}
                      className="rounded border-gray-300"
                    />
                    <label htmlFor="useAiMapping" className="text-sm font-medium text-blue-900">
                      {t('products.enableSmartMapping')}
                    </label>
                  </div>
                </div>

                {useAiMapping && (
                  <div className="bg-white/50 rounded-md p-3 space-y-2">
                    <div className="flex items-center gap-2 text-green-700">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium">{t('products.advancedAnalysisActive')}</span>
                    </div>
                    <div className="text-xs text-gray-600 space-y-1">
                      <div>✅ {t('products.intelligentColumnAnalysis')}</div>
                      <div>✅ {t('products.contentPatternRecognition')}</div>
                      <div>✅ {t('products.automaticFallback')}</div>
                    </div>
                  </div>
                )}

                {isAnalyzingColumns && (
                  <div className="bg-white/50 rounded-md p-3 space-y-2">
                    <div className="flex items-center gap-2 text-blue-700">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span className="text-sm font-medium">{t('products.analyzingColumnStructure')}</span>
                    </div>
                    <div className="text-xs text-gray-600">
                      {t('products.mayTakeFewMoments')}
                    </div>
                  </div>
                )}
              </div>

              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Need a template?</h4>
                    <p className="text-xs text-gray-600 mt-1">
                      Download a template file with the correct headers for importing products.
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDownloadTemplate}
                    className="bg-white hover:bg-gray-50"
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Download Template
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="review" className="flex-1 overflow-auto p-4">
            {/* Debug Information */}
            {process.env.NODE_ENV === 'development' && (
              <div className="bg-gray-100 p-2 rounded text-xs mb-4">
                <strong>Debug Info:</strong>
                ImportedData: {importedData.length},
                MappedProducts: {mappedProducts.length},
                ValidationResults: {validationResults ? 'Present' : 'Missing'},
                AIResult: {aiMappingResult ? 'Present' : 'Missing'}
              </div>
            )}

            {mappedProducts.length > 0 && dataAnalysis && (
              <div className="space-y-4">
                {/* Data Analysis Summary */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-blue-50 rounded-lg">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{dataAnalysis.totalRows}</div>
                    <div className="text-sm text-gray-600">Total Rows</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{dataAnalysis.detectedArticleNumbers.length}</div>
                    <div className="text-sm text-gray-600">Article Numbers</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">{dataAnalysis.dataQuality.incompleteRows}</div>
                    <div className="text-sm text-gray-600">Need Fetch</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{dataAnalysis.dataQuality.duplicateArticleNumbers.length}</div>
                    <div className="text-sm text-gray-600">Duplicates</div>
                  </div>
                </div>

                {/* Fetch Progress */}
                {isFetchingData && (
                  <div className="p-4 bg-yellow-50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span className="font-medium">Fetching product data from TecDoc...</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(fetchProgress.completed / fetchProgress.total) * 100}%` }}
                      />
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      {fetchProgress.completed} of {fetchProgress.total} completed
                    </div>
                  </div>
                )}

                {/* Column Analysis Summary */}
                {aiMappingResult && (
                  <AIMappingSummary aiMappingResult={aiMappingResult} />
                )}

                {/* Enhanced Review Table */}
                {mappedProducts && Array.isArray(mappedProducts) && mappedProducts.length > 0 ? (
                  <ImportReviewTable
                    products={mappedProducts}
                    onProductUpdate={handleProductUpdate}
                    onBatchFetchData={handleBatchFetchData}
                    categoryId={categoryId}
                    isLoading={isFetchingData}
                  />
                ) : (
                  <div className="border rounded-lg p-8 text-center">
                    <div className="text-muted-foreground">
                      <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">No Products to Review</h3>
                      <p className="text-sm">
                        There was an issue processing your file. Please try uploading again or check the file format.
                      </p>
                    </div>
                  </div>
                )}

                {/* Navigation Actions */}
                <div className="flex items-center justify-between pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => setActiveTab('upload')}
                    disabled={isImporting || isFetchingData || isEnriching}
                  >
                    Back to Upload
                  </Button>
                  <Button
                    onClick={() => setActiveTab('enrich')}
                    disabled={!mappedProducts.length || isFetchingData || isEnriching}
                  >
                    Continue to Enrich Data
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>

          {/* New Enrichment Tab */}
          <TabsContent value="enrich" className="flex-1 overflow-auto p-4 space-y-6">
            <div className="space-y-6">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <Sparkles className="w-5 h-5 text-blue-600" />
                    Data Enrichment & Validation
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Enhance your products with comprehensive data from TecDoc API and intelligent processing
                  </p>
                </div>
                {!isEnriching && !enrichmentProgress && (
                  <Button
                    onClick={handleComprehensiveEnrichment}
                    disabled={!mappedProducts.length}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Sparkles className="w-4 h-4 mr-2" />
                    Start Enrichment
                  </Button>
                )}
              </div>

              {/* AI Mapping Summary */}
              {aiMappingResult && (
                <AIMappingSummary aiMappingResult={aiMappingResult} />
              )}

              {/* Enrichment Progress */}
              {enrichmentProgress && (
                <EnrichmentProgressStepper progress={enrichmentProgress} />
              )}

              {/* Enrichment Results Summary */}
              {enrichmentSession && enrichmentSession.progress.step === 'complete' && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-medium text-green-900 flex items-center gap-2">
                      <CheckCircle2 className="w-5 h-5" />
                      Enrichment Complete
                    </h4>
                    <Badge variant="outline" className="bg-green-100 text-green-800">
                      {enrichmentSession.totalProducts} Products Processed
                    </Badge>
                  </div>

                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="text-center bg-white rounded-lg p-3">
                      <div className="text-2xl font-bold text-green-600">{enrichmentSession.successCount}</div>
                      <div className="text-xs text-green-700">Fully Enriched</div>
                    </div>
                    <div className="text-center bg-white rounded-lg p-3">
                      <div className="text-2xl font-bold text-blue-600">{enrichmentSession.partialCount}</div>
                      <div className="text-xs text-blue-700">Partially Enriched</div>
                    </div>
                    <div className="text-center bg-white rounded-lg p-3">
                      <div className="text-2xl font-bold text-orange-600">{enrichmentSession.failedCount}</div>
                      <div className="text-xs text-orange-700">Need Review</div>
                    </div>
                  </div>

                  <div className="text-sm text-green-700">
                    ✅ Products now have enhanced names, descriptions, and specifications
                    <br />
                    ✅ TecDoc data integrated where available
                    <br />
                    ✅ All products validated for marketplace readiness
                  </div>
                </div>
              )}

              {/* Enhanced Product Preview */}
              {mappedProducts.length > 0 && (
                <div className="border rounded-lg overflow-hidden">
                  <div className="bg-gray-50 px-4 py-3 border-b">
                    <h4 className="font-medium text-gray-900">Enhanced Product Preview</h4>
                    <p className="text-sm text-gray-600">Review your enriched products before final import</p>
                  </div>
                  <div className="max-h-64 overflow-y-auto">
                    <table className="w-full text-sm">
                      <thead className="bg-gray-50 sticky top-0">
                        <tr>
                          <th className="p-2 text-left border-r">Product Name</th>
                          <th className="p-2 text-left border-r">Manufacturer</th>
                          <th className="p-2 text-left border-r">Part Number</th>
                          <th className="p-2 text-left">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {mappedProducts.slice(0, 10).map((product, index) => (
                          <tr key={index} className="border-b hover:bg-gray-50">
                            <td className="p-2 border-r">
                              <div className="font-medium">{product.name}</div>
                              {product.dataFetchedFromTecDoc && (
                                <div className="text-xs text-green-600">✓ TecDoc Enhanced</div>
                              )}
                            </td>
                            <td className="p-2 border-r">{product.manufacturer || '-'}</td>
                            <td className="p-2 border-r font-mono text-xs">{product.partArticleNumber || '-'}</td>
                            <td className="p-2">
                              <Badge variant={product.dataFetchedFromTecDoc ? 'default' : 'secondary'} className="text-xs">
                                {product.dataFetchedFromTecDoc ? 'Enhanced' : 'Standard'}
                              </Badge>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    {mappedProducts.length > 10 && (
                      <div className="p-3 text-center text-sm text-gray-600 bg-gray-50">
                        ... and {mappedProducts.length - 10} more products
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Navigation Actions */}
              <div className="flex items-center justify-between pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() => setActiveTab('review')}
                  disabled={isEnriching}
                >
                  Back to Review
                </Button>
                <Button
                  onClick={() => setActiveTab('final-review')}
                  disabled={!enrichmentSession || enrichmentSession.progress.step !== 'complete' || isEnriching}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Continue to Final Review
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* Final Review Tab */}
          <TabsContent value="final-review" className="flex-1 overflow-auto p-4">
            {enrichmentSession && enrichmentSession.products && (
              <FinalReviewTable
                enrichmentResults={enrichmentSession.products}
                onProductUpdate={(index, updatedProduct) => {
                  // Update the product in the enrichment session
                  if (enrichmentSession.products[index]) {
                    enrichmentSession.products[index].enrichedProduct = updatedProduct;
                    setMappedProducts(enrichmentSession.products.map(p => p.enrichedProduct));

                    // Re-validate
                    const validation = validateImportedProducts(enrichmentSession.products.map(p => p.enrichedProduct));
                    setValidationResults(validation);
                  }
                }}
                onProceedToImport={() => setActiveTab('complete')}
                onBackToEnrichment={() => setActiveTab('enrich')}
              />
            )}
          </TabsContent>

          <TabsContent value="complete" className="flex-1 overflow-auto p-4 space-y-6">
            {validationResults && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Complete Import</h3>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">
                      {validationResults.valid.length} ready to import
                    </Badge>
                    {validationResults.invalid.length > 0 && (
                      <Badge variant="destructive">
                        {validationResults.invalid.length} require attention
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Final Summary */}
                <div className="grid grid-cols-3 gap-4 p-4 bg-green-50 rounded-lg">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{validationResults.valid.length}</div>
                    <div className="text-sm text-gray-600">Ready to Import</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {validationResults.valid.filter(p => p.dataFetchedFromTecDoc).length}
                    </div>
                    <div className="text-sm text-gray-600">TecDoc Enhanced</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">
                      {validationResults.valid.filter(p => !p.dataFetchedFromTecDoc).length}
                    </div>
                    <div className="text-sm text-gray-600">Manual Entry</div>
                  </div>
                </div>

                {/* Issues to Address */}
                {validationResults.invalid.length > 0 && (
                  <div className="p-4 bg-red-50 rounded-lg">
                    <h4 className="font-medium text-red-600 mb-2 flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4" />
                      Issues Requiring Attention ({validationResults.invalid.length})
                    </h4>
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {validationResults.invalid.map((item, index) => (
                        <div key={index} className="text-sm">
                          <span className="font-medium">{item.product.name || 'Unnamed Product'}:</span>
                          <span className="text-red-600 ml-2">{item.errors.join(', ')}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Import Actions */}
                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="text-sm text-muted-foreground">
                    {validationResults.valid.length > 0
                      ? `Ready to import ${validationResults.valid.length} valid products`
                      : 'No valid products to import'
                    }
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setActiveTab('review')}
                      disabled={isImporting}
                    >
                      Back to Review
                    </Button>
                    <Button
                      onClick={handleImport}
                      disabled={validationResults.valid.length === 0 || isImporting}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      {isImporting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Importing... {importProgress}%
                        </>
                      ) : (
                        `Import ${validationResults.valid.length} Products`
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>

        <DialogFooter className="border-t bg-gray-50/50 pt-4 pb-4">
          <div className="flex justify-between w-full">
            <Button
              variant="outline"
              onClick={() => handleDialogClose(false)}
              disabled={isUploading || isImporting || isEnriching}
              className="bg-white hover:bg-gray-50"
            >
              Cancel
            </Button>

            {/* Dynamic action button based on current tab */}
            {activeTab === 'upload' && (
              <Button
                onClick={handleFileUpload}
                disabled={!file || isUploading}
                className="min-w-[140px]"
              >
                {isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {uploadProgress < 30 ? t('products.reading') :
                     uploadProgress < 60 ? t('products.analyzing') :
                     uploadProgress < 90 ? t('products.processing') :
                     t('products.finalizing')}
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Upload and Review
                  </>
                )}
              </Button>
            )}

            {activeTab === 'complete' && (
              <div className="flex items-center gap-2">
                {isImporting && (
                  <div className="flex items-center gap-2 mr-2">
                    <Progress value={importProgress} className="w-24 h-2" />
                    <span className="text-xs text-muted-foreground">{importProgress}%</span>
                  </div>
                )}
                <Button
                  onClick={handleImport}
                  disabled={!validationResults?.valid.length || isImporting}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isImporting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Importing...
                    </>
                  ) : (
                    <>
                      <CheckCircle2 className="mr-2 h-4 w-4" />
                      Import {validationResults?.valid.length} Products
                    </>
                  )}
                </Button>
              </div>
            )}
          </div>
        </DialogFooter>
      </DialogContent>

      {/* Enhanced Import Dialog */}
      <EnhancedImportDialog
        open={showEnhancedImport}
        onOpenChange={setShowEnhancedImport}
        onImportComplete={handleEnhancedImportComplete}
      />
    </Dialog>
  );
};
