/**
 * Centralized Categories Configuration
 * 
 * This file contains all category and subcategory data used throughout the application.
 * It ensures consistency across all components and provides a single source of truth.
 */

export interface CategoryData {
  id: string;
  name: string;
  prefix: string;
  subcategories: SubcategoryData[];
}

export interface SubcategoryData {
  id: string;
  name: string;
}

/**
 * Complete categories configuration
 */
export const CATEGORIES: CategoryData[] = [
  {
    id: 'tyres',
    name: 'Tyres & Related Products',
    prefix: 'TYR',
    subcategories: [
      { id: 'tyres', name: 'Tyres' },
      { id: 'wheels', name: 'Wheels' },
      { id: 'rims', name: 'Rims' },
      { id: 'wheel-accessories', name: 'Wheel Accessories' },
      { id: 'tyre-accessories', name: 'Tyre Accessories' },
      { id: 'valve-stems', name: 'Valve Stems' },
      { id: 'wheel-weights', name: 'Wheel Weights' },
      { id: 'tyre-pressure-monitoring', name: 'Tyre Pressure Monitoring' },
      { id: 'wheel-spacers', name: 'Wheel Spacers' },
      { id: 'lug-nuts', name: 'Lug Nuts' },
      { id: 'wheel-bolts', name: 'Wheel Bolts' },
      { id: 'hub-caps', name: 'Hub Caps' },
      { id: 'wheel-covers', name: 'Wheel Covers' },
      { id: 'tyre-chains', name: 'Tyre Chains' },
      { id: 'run-flat-inserts', name: 'Run Flat Inserts' },
      { id: 'wheel-alignment', name: 'Wheel Alignment' },
      { id: 'balancing-weights', name: 'Balancing Weights' },
      { id: 'tyre-repair', name: 'Tyre Repair' }
    ]
  },
  {
    id: 'brakes',
    name: 'Brake Parts & Systems',
    prefix: 'BRK',
    subcategories: [
      { id: 'brake-discs', name: 'Brake Discs' },
      { id: 'brake-pads', name: 'Brake Pads' },
      { id: 'brake-shoes', name: 'Brake Shoes' },
      { id: 'brake-drums', name: 'Brake Drums' },
      { id: 'brake-calipers', name: 'Brake Calipers' },
      { id: 'brake-cylinders', name: 'Brake Cylinders' },
      { id: 'brake-hoses', name: 'Brake Hoses' },
      { id: 'brake-lines', name: 'Brake Lines' },
      { id: 'brake-fluid', name: 'Brake Fluid' },
      { id: 'brake-boosters', name: 'Brake Boosters' },
      { id: 'master-cylinders', name: 'Master Cylinders' },
      { id: 'brake-sensors', name: 'Brake Sensors' },
      { id: 'abs-components', name: 'ABS Components' },
      { id: 'parking-brake', name: 'Parking Brake' },
      { id: 'brake-hardware', name: 'Brake Hardware' },
      { id: 'brake-tools', name: 'Brake Tools' },
      { id: 'performance-brakes', name: 'Performance Brakes' },
      { id: 'brake-maintenance', name: 'Brake Maintenance' }
    ]
  },
  {
    id: 'filters',
    name: 'Filters & Filtration',
    prefix: 'FLT',
    subcategories: [
      { id: 'air-filters', name: 'Air Filters' },
      { id: 'oil-filters', name: 'Oil Filters' },
      { id: 'fuel-filters', name: 'Fuel Filters' },
      { id: 'cabin-filters', name: 'Cabin Filters' },
      { id: 'hydraulic-filters', name: 'Hydraulic Filters' },
      { id: 'transmission-filters', name: 'Transmission Filters' },
      { id: 'coolant-filters', name: 'Coolant Filters' },
      { id: 'power-steering-filters', name: 'Power Steering Filters' },
      { id: 'differential-filters', name: 'Differential Filters' },
      { id: 'breather-filters', name: 'Breather Filters' },
      { id: 'intake-filters', name: 'Intake Filters' },
      { id: 'exhaust-filters', name: 'Exhaust Filters' },
      { id: 'particulate-filters', name: 'Particulate Filters' },
      { id: 'adblue-filters', name: 'AdBlue Filters' },
      { id: 'vacuum-filters', name: 'Vacuum Filters' },
      { id: 'filter-housings', name: 'Filter Housings' },
      { id: 'filter-elements', name: 'Filter Elements' },
      { id: 'filter-accessories', name: 'Filter Accessories' }
    ]
  },
  {
    id: 'oils-fluids',
    name: 'Oils & Fluids',
    prefix: 'PROD',
    subcategories: [
      { id: 'engine-lubrication-oil', name: 'Engine Lubrication Oil' },
      { id: 'hydraulic-system-oil', name: 'Hydraulic System Oil' },
      { id: 'automatic-transmission-fluid-atf', name: 'Automatic Transmission Fluid (ATF)' },
      { id: 'coolant-antifreeze-liquid', name: 'Coolant & Antifreeze Liquid' },
      { id: 'brake-system-fluid', name: 'Brake System Fluid' },
      { id: 'power-steering-hydraulic-fluid', name: 'Power Steering Hydraulic Fluid' },
      { id: 'transmission-gear-oil', name: 'Transmission Gear Oil' },
      { id: 'windshield-washer-solution', name: 'Windshield Washer Solution' },
      { id: 'distilled-water-automotive', name: 'Distilled Water for Automotive Use' },
      { id: 'diesel-exhaust-fluid-adblue', name: 'Diesel Exhaust Fluid (AdBlue Equivalent)' },
      { id: 'oil-treatment-performance-enhancer', name: 'Oil Treatment & Performance Enhancer' },
      { id: 'soot-particle-filter-cleaner', name: 'Soot & Particle Filter Cleaner' },
      { id: 'transmission-treatment-additives', name: 'Transmission Treatment Additives' }
    ]
  },
  // NEW CATEGORIES ADDED FROM DATABASE MIGRATIONS
  {
    id: 'bearings',
    name: 'Bearings',
    prefix: 'BRG',
    subcategories: [
      { id: 'wheel-bearing', name: 'Wheel bearing' },
      { id: 'propshaft-bearing', name: 'Propshaft bearing' },
      { id: 'shaft-seal-wheel-hub', name: 'Shaft seal, wheel hub' },
      { id: 'bearing-stub-axle', name: 'Bearing, stub axle' },
      { id: 'intermediate-shaft-bearing', name: 'Intermediate shaft bearing' },
      { id: 'clutch-release-bearing', name: 'Clutch release bearing' },
      { id: 'central-slave-cylinder', name: 'Central slave cylinder' },
      { id: 'sleeve', name: 'Sleeve' },
      { id: 'crankshaft-bearing', name: 'Crankshaft bearing' },
      { id: 'main-bearings-crankshaft', name: 'Main bearings, crankshaft' },
      { id: 'camshaft-bushes', name: 'Camshaft bushes' },
      { id: 'drive-bearing-alternator', name: 'Drive bearing, alternator' },
      { id: 'gearbox-bearing', name: 'Gearbox bearing' },
      { id: 'flange-lid-manual-transmission', name: 'Flange lid, manual transmission' }
    ]
  },
  {
    id: 'repair-kits',
    name: 'Repair kits',
    prefix: 'RK',
    subcategories: [
      { id: 'repair-set-crankcase-breather', name: 'Repair set, crankcase breather' },
      { id: 'valve-engine-block-breather', name: 'Valve, engine block breather' },
      { id: 'accessory-kit-brake-shoes', name: 'Accessory kit, brake shoes' },
      { id: 'brake-caliper-repair-kit', name: 'Brake caliper repair kit' },
      { id: 'anti-roll-bar-stabiliser-kit', name: 'Anti roll bar stabiliser kit' },
      { id: 'brake-caliper-repair-kit', name: 'Brake caliper repair kit' },
      { id: 'gear-lever-repair-kit', name: 'Gear lever repair kit' }
    ]
  },
  {
    id: 'lighting',
    name: 'Lighting',
    prefix: 'LT',
    subcategories: [
      { id: 'rear-lights', name: 'Rear lights' },
      { id: 'headlights', name: 'Headlights' },
      { id: 'bulb-spotlight', name: 'Bulb, spotlight' },
      { id: 'bulb-indicator', name: 'Bulb, indicator' },
      { id: 'bulb-interior-light', name: 'Bulb, interior light' },
      { id: 'bulb-licence-plate-light', name: 'Bulb, licence plate light' },
      { id: 'bulb-park-position-light', name: 'Bulb, park-/position light' },
      { id: 'bulb-stop-light', name: 'Bulb, stop light' },
      { id: 'bulb-tail-light', name: 'Bulb, tail light' },
      { id: 'bulb-reverse-light', name: 'Bulb, reverse light' }
    ]
  },
  {
    id: 'tuning',
    name: 'Tuning',
    prefix: 'TN',
    subcategories: [
      { id: 'guide-sleeve-kit-brake-caliper', name: 'Guide sleeve kit, brake caliper' }
    ]
  },
  {
    id: 'fasteners',
    name: 'Fasteners',
    prefix: 'FS',
    subcategories: [
      { id: 'shaft-seal-wheel-hub', name: 'Shaft seal, wheel hub' },
      { id: 'gear-linkage-repair-kit', name: 'Gear linkage repair kit' },
      { id: 'main-bearings-crankshaft', name: 'Main bearings, crankshaft' },
      { id: 'gear-lever-repair-kit', name: 'Gear lever repair kit' },
      { id: 'guide-sleeve-kit-brake-caliper', name: 'Guide sleeve kit, brake caliper' }
    ]
  }
];

/**
 * Get category data by ID
 */
export const getCategoryById = (categoryId: string): CategoryData | undefined => {
  return CATEGORIES.find(cat => cat.id === categoryId);
};

/**
 * Get category prefix by ID
 */
export const getCategoryPrefix = (categoryId: string): string => {
  const category = getCategoryById(categoryId);
  return category?.prefix || 'PROD';
};

/**
 * Get all categories except tyres (for unified table)
 */
export const getAllOtherCategories = (): CategoryData[] => {
  return CATEGORIES.filter(cat => cat.id !== 'tyres');
};

/**
 * Get all subcategories for a category
 */
export const getSubcategoriesForCategory = (categoryId: string): SubcategoryData[] => {
  const category = getCategoryById(categoryId);
  return category?.subcategories || [];
};

/**
 * Get category name by ID
 */
export const getCategoryName = (categoryId: string): string => {
  const category = getCategoryById(categoryId);
  return category?.name || categoryId;
};

/**
 * Check if a category exists
 */
export const categoryExists = (categoryId: string): boolean => {
  return CATEGORIES.some(cat => cat.id === categoryId);
};

/**
 * Get all category IDs
 */
export const getAllCategoryIds = (): string[] => {
  return CATEGORIES.map(cat => cat.id);
};

/**
 * Get categories for unified table (all except tyres)
 */
export const getUnifiedTableCategories = (): string[] => {
  return getAllOtherCategories().map(cat => cat.id);
};
