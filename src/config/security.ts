/**
 * AROUZ MARKET - Production Security Configuration
 * 
 * This module provides security configurations and utilities for production deployment.
 * Implements industry-standard security practices for web applications.
 */

// Environment-based security settings
const isProduction = import.meta.env.VITE_APP_ENV === 'production';
const enableCSP = import.meta.env.VITE_ENABLE_CSP === 'true';
const enableHTTPSOnly = import.meta.env.VITE_ENABLE_HTTPS_ONLY === 'true';
const disableConsoleLogs = import.meta.env.VITE_DISABLE_CONSOLE_LOGS === 'true';

/**
 * Security Configuration Object
 */
export const securityConfig = {
  // Content Security Policy
  csp: {
    enabled: enableCSP,
    directives: {
      'default-src': ["'self'"],
      'script-src': [
        "'self'",
        "'unsafe-inline'", // Required for React
        "'unsafe-eval'", // Required for some libraries
        'https://maps.googleapis.com',
        'https://www.google.com',
        'https://www.gstatic.com'
      ],
      'style-src': [
        "'self'",
        "'unsafe-inline'", // Required for styled-components
        'https://fonts.googleapis.com'
      ],
      'font-src': [
        "'self'",
        'https://fonts.gstatic.com'
      ],
      'img-src': [
        "'self'",
        'data:',
        'https:',
        'blob:'
      ],
      'connect-src': [
        "'self'",
        'https://*.supabase.co',
        'https://maps.googleapis.com',
        'https://api.dexatel.com'
      ],
      'frame-src': [
        "'self'",
        'https://www.google.com'
      ],
      'object-src': ["'none'"],
      'base-uri': ["'self'"],
      'form-action': ["'self'"]
    }
  },

  // HTTPS Configuration
  https: {
    enabled: enableHTTPSOnly,
    hsts: {
      maxAge: 31536000, // 1 year
      includeSubDomains: true,
      preload: true
    }
  },

  // Console Security - TEMPORARILY ALLOW LOGS FOR AUTH DEBUGGING
  console: {
    disableInProduction: false, // DISABLED to debug authentication issues
    allowedMethods: isProduction ? ['log', 'warn', 'error', 'info'] : ['log', 'warn', 'error', 'info', 'debug']
  },

  // API Security
  api: {
    timeout: 5000,
    maxRetries: 3,
    enableCSRF: true,
    enableRateLimit: true
  },

  // Input Validation
  validation: {
    maxFileSize: 10 * 1024 * 1024, // 10MB (for images only)
    maxImportFileSize: 500 * 1024 * 1024, // 500MB for CSV/Excel imports
    allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp'],
    allowedImportTypes: ['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
    maxStringLength: 10000,
    enableSanitization: true
  }
};

/**
 * Initialize security measures
 */
export function initializeSecurity(): void {
  // Disable console logs in production
  if (securityConfig.console.disableInProduction && isProduction) {
    const allowedMethods = securityConfig.console.allowedMethods;
    
    Object.keys(console).forEach((method) => {
      if (!allowedMethods.includes(method)) {
        (console as any)[method] = () => {};
      }
    });
  }

  // Add security event listeners
  if (isProduction) {
    // Prevent right-click context menu in production
    document.addEventListener('contextmenu', (e) => {
      e.preventDefault();
    });

    // Prevent F12, Ctrl+Shift+I, Ctrl+U
    document.addEventListener('keydown', (e) => {
      if (
        e.key === 'F12' ||
        (e.ctrlKey && e.shiftKey && e.key === 'I') ||
        (e.ctrlKey && e.key === 'u')
      ) {
        e.preventDefault();
      }
    });

    // Detect developer tools
    let devtools = { open: false, orientation: null };
    const threshold = 160;

    setInterval(() => {
      if (
        window.outerHeight - window.innerHeight > threshold ||
        window.outerWidth - window.innerWidth > threshold
      ) {
        if (!devtools.open) {
          devtools.open = true;
          console.warn('Developer tools detected. Please close them for security.');
        }
      } else {
        devtools.open = false;
      }
    }, 500);
  }
}

/**
 * Sanitize user input
 */
export function sanitizeInput(input: string): string {
  if (!securityConfig.validation.enableSanitization) {
    return input;
  }

  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim()
    .substring(0, securityConfig.validation.maxStringLength);
}

/**
 * Validate file upload
 */
export function validateFileUpload(file: File): { valid: boolean; error?: string } {
  // Check file size for images
  if (file.type.startsWith('image/') && file.size > securityConfig.validation.maxFileSize) {
    return {
      valid: false,
      error: `Image size exceeds ${securityConfig.validation.maxFileSize / (1024 * 1024)}MB limit`
    };
  }

  // Check file type for images
  if (file.type.startsWith('image/') && !securityConfig.validation.allowedImageTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Invalid image file type'
    };
  }

  return { valid: true };
}

/**
 * Validate import file upload (CSV/Excel)
 */
export function validateImportFileUpload(file: File): { valid: boolean; error?: string } {
  // Check file size for import files
  if (file.size > securityConfig.validation.maxImportFileSize) {
    return {
      valid: false,
      error: `Import file size exceeds ${securityConfig.validation.maxImportFileSize / (1024 * 1024)}MB limit`
    };
  }

  // Check file type for import files
  const isValidImportType = securityConfig.validation.allowedImportTypes.includes(file.type) ||
    file.name.toLowerCase().endsWith('.csv') ||
    file.name.toLowerCase().endsWith('.xlsx') ||
    file.name.toLowerCase().endsWith('.xls');

  if (!isValidImportType) {
    return {
      valid: false,
      error: 'Invalid import file type. Please upload CSV or Excel files only.'
    };
  }

  return { valid: true };
}

/**
 * Generate secure random string
 */
export function generateSecureToken(length: number = 32): string {
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Check if current connection is secure
 */
export function isSecureConnection(): boolean {
  return location.protocol === 'https:' || location.hostname === 'localhost';
}

/**
 * Enforce HTTPS redirect
 */
export function enforceHTTPS(): void {
  if (securityConfig.https.enabled && !isSecureConnection() && location.hostname !== 'localhost') {
    location.replace(`https:${location.href.substring(location.protocol.length)}`);
  }
}

/**
 * Security headers for fetch requests
 */
export function getSecurityHeaders(): Record<string, string> {
  const headers: Record<string, string> = {
    'X-Requested-With': 'XMLHttpRequest',
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  };

  if (securityConfig.api.enableCSRF) {
    headers['X-CSRF-Token'] = generateSecureToken(16);
  }

  return headers;
}

/**
 * Rate limiting utility
 */
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  private readonly maxRequests: number;
  private readonly windowMs: number;

  constructor(maxRequests: number = 100, windowMs: number = 60000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }

  isAllowed(identifier: string): boolean {
    // DISABLED: Rate limiting completely removed - always allow unlimited attempts
    return true;
  }
}

export const rateLimiter = new RateLimiter();

// Initialize security on module load
if (typeof window !== 'undefined') {
  initializeSecurity();
  enforceHTTPS();
}
