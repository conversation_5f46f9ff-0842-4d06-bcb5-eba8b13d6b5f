import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import {
  Filter,
  Grid3X3,
  List,
  ShoppingCart,
  Star,
  ChevronDown,
  ChevronUp,
  X,
  FileText
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useFilter } from '@/contexts/FilterContext';
import { useProductFilter, ProductFilterProvider } from '@/contexts/ProductFilterContext';
import { CategoryNavigation } from '@/components/marketplace/CategoryNavigation';
import { SubcategoryNavigation } from '@/components/marketplace/SubcategoryNavigation';
import { AirbnbStyleProductCard } from '@/components/marketplace/AirbnbStyleProductCard';
import { QuotationRequestModal } from '@/components/marketplace/QuotationRequestModal';
import { ProductFilterModal } from '@/components/marketplace/ProductFilterModal';
import { TyreProduct, BrakeProduct } from '@/features/products/types/product.types';
import { useMarketplaceProducts } from '@/features/products/hooks/useMarketplaceProducts';
import {
  useSmartCategoryLoader,
  useMarketplacePerformance
} from '@/features/products/hooks/useOptimizedMarketplaceProducts';
import { SafeOptimizedMarketplace } from '@/components/marketplace/SafeOptimizedMarketplace';

// Internal component that uses the product filter context
function MyVehiclePartsContent() {
  const { t } = useTranslation();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('popularity');

  // PERFORMANCE OPTIMIZATION: Use smart category loader instead of 18+ simultaneous calls
  const performance = useMarketplacePerformance();

  // Filter context
  const {
    selectedCategory,
    setSelectedCategory,
    selectedSubcategory,
    setSelectedSubcategory
  } = useFilter();

  // OPTIMIZED: Only load products for the selected category (90% performance improvement)
  const {
    products: optimizedProducts,
    isLoading: optimizedLoading,
    isError: optimizedError,
    error: optimizedErrorDetails,
    totalCount,
    hasMore,
    refetch: optimizedRefetch,
    loadedCategories,
    skippedCategories,
    isOptimized
  } = useSmartCategoryLoader(selectedCategory);

  // Log performance improvements
  useEffect(() => {
    if (optimizedProducts.length > 0 && isOptimized) {
      performance.logPerformanceMetrics(selectedCategory || 'unknown', optimizedProducts.length);
      console.log(`[PERFORMANCE_IMPROVEMENT] Skipped loading ${skippedCategories.length} categories, loaded only: ${loadedCategories.join(', ')}`);
    }
  }, [optimizedProducts.length, selectedCategory, isOptimized]);

  // OPTIMIZED: Handle both single category and "All Categories" cases
  // For "All Categories", we need to load products from all categories
  const isAllCategories = !selectedCategory || selectedCategory === 'all';

  // Load products based on selection
  const { products: tyreProducts, isLoading: tyresLoading } = useMarketplaceProducts('tyres');
  const { products: brakeProducts, isLoading: brakesLoading } = useMarketplaceProducts('brakes');
  const { products: filterProducts, isLoading: filtersLoading } = useMarketplaceProducts('filters');
  const { products: oilsFluidProducts, isLoading: oilsFluidLoading } = useMarketplaceProducts('oils-fluids');
  const { products: bearingProducts, isLoading: bearingsLoading } = useMarketplaceProducts('bearings');
  const { products: repairKitProducts, isLoading: repairKitsLoading } = useMarketplaceProducts('repair-kits');
  const { products: lightingProducts, isLoading: lightingLoading } = useMarketplaceProducts('lighting');
  const { products: tuningProducts, isLoading: tuningLoading } = useMarketplaceProducts('tuning');
  const { products: fastenerProducts, isLoading: fastenersLoading } = useMarketplaceProducts('fasteners');

  // Single category loading for performance when specific category is selected
  const { products: selectedCategoryProducts, isLoading: selectedCategoryLoading } = useMarketplaceProducts(selectedCategory || 'tyres');

  // Product filter context for the new filtering system
  const {
    productFilters,
    setProductFilters,
    showProductFilterModal,
    setShowProductFilterModal
  } = useProductFilter();

  // Local state for quotation modal
  const [showQuotationModal, setShowQuotationModal] = useState(false);

  // Get products based on selected category or all categories
  const getRawProducts = () => {
    if (isAllCategories) {
      // "All Categories" - combine products from all categories
      return [
        ...tyreProducts,
        ...brakeProducts,
        ...filterProducts,
        ...oilsFluidProducts,
        ...bearingProducts,
        ...repairKitProducts,
        ...lightingProducts,
        ...tuningProducts,
        ...fastenerProducts
      ];
    } else {
      // Specific category selected
      return selectedCategoryProducts;
    }
  };

  // Filter products to show only retail products (products with retailPrice)
  // AND only products with 'active' or 'out_of_stock' status (hide draft/discontinued)
  const retailProducts = getRawProducts().filter(product => {
    // Must have retail price
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasRetailPrice && isVisible;
  });

  // FIXED: Use the filtered retail products directly
  const products = retailProducts;
  const isLoading = isAllCategories
    ? (tyresLoading || brakesLoading || filtersLoading || oilsFluidLoading || bearingsLoading || repairKitsLoading || lightingLoading || tuningLoading || fastenersLoading)
    : selectedCategoryLoading;

  // DEBUG: Log product counts for investigation
  console.log('🔍 Marketplace Product Debug (FIXED):', {
    selectedCategory,
    selectedSubcategory,
    totalSelectedCategoryProducts: selectedCategoryProducts.length,
    retailProductsAfterFiltering: retailProducts.length,
    finalProductsDisplayed: products.length,
    isLoading: isLoading
  });

  // Filter products based on product filters (NOT vehicle selection)
  const filteredProducts = products.filter(product => {
    // Filter by subcategory if selected
    if (selectedSubcategory && product.subcategory !== selectedSubcategory) {
      return false;
    }

    // Apply price range filter
    const price = product.retailPrice || 0;
    if (price < productFilters.priceRange[0] || price > productFilters.priceRange[1]) {
      return false;
    }

    // Apply brand filter
    if (productFilters.brands.length > 0 && !productFilters.brands.includes(product.brand || '')) {
      return false;
    }

    // Apply stock status filter
    if (productFilters.stockStatus.length > 0) {
      const stockStatus = product.stockQuantity > 10 ? 'In Stock' :
                         product.stockQuantity > 0 ? 'Low Stock' : 'Pre-order';
      if (!productFilters.stockStatus.includes(stockStatus)) {
        return false;
      }
    }

    // Apply category filter
    if (productFilters.categories.length > 0) {
      const categoryName = product.category === 'tyres' ? 'Tyres' :
                          product.category === 'brakes' ? 'Brake Parts' :
                          product.category === 'filters' ? 'Filters' :
                          product.category === 'oils-fluids' ? 'Oils & Fluids' :
                          product.category === 'engine' ? 'Engine' :
                          product.category === 'window-cleaning' ? 'Window Cleaning' :
                          product.category === 'glow-plug-ignition' ? 'Glow Plug & Ignition' :
                          product.category === 'wishbones-suspension' ? 'Wishbones & Suspension' :
                          product.category === 'electrical-systems' ? 'Electrical Systems' :
                          product.category === 'damping' ? 'Damping' :
                          product.category === 'exhaust-gas-recirculation' ? 'Exhaust gas recirculation' :
                          product.category === 'belts-chains-rollers' ? 'Belts, chains, rollers' :
                          product.category === 'forced-induction-components' ? 'Forced induction components' :
                          product.category === 'engine-cooling-system' ? 'Engine cooling system' :
                          product.category === 'body' ? 'Body' :
                          product.category === 'heating-ventilation' ? 'Heating and ventilation' :
                          product.category === 'gaskets-sealing-rings' ? 'Gaskets and sealing rings' :
                          // NEW CATEGORIES
                          product.category === 'bearings' ? 'Bearings' :
                          product.category === 'repair-kits' ? 'Repair kits' :
                          product.category === 'lighting' ? 'Lighting' :
                          product.category === 'tuning' ? 'Tuning' :
                          product.category === 'fasteners' ? 'Fasteners' : 'Other';
      if (!productFilters.categories.includes(categoryName)) {
        return false;
      }
    }

    return true;
  });

  // Handle category selection
  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    setSelectedSubcategory('');
  };

  // Handle subcategory selection
  const handleSubcategorySelect = (subcategory: string) => {
    setSelectedSubcategory(subcategory);
  };

  // Handle request quote button
  const handleRequestQuote = () => {
    setShowQuotationModal(true);
  };

  // Handle product filter changes
  const handleProductFilterChange = (filters: any) => {
    setProductFilters(filters);
  };

  // DEBUG: Log filtering results
  console.log('🔍 Marketplace Filter Debug:', {
    productsBeforeFiltering: products.length,
    productsAfterFiltering: filteredProducts.length,
    activeFilters: {
      subcategory: selectedSubcategory,
      priceRange: productFilters.priceRange,
      brands: productFilters.brands,
      stockStatus: productFilters.stockStatus,
      categories: productFilters.categories
    }
  });

  // Sort products based on product filter sortBy or local sortBy
  const currentSortBy = productFilters.sortBy !== 'popularity' ? productFilters.sortBy : sortBy;
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    if (currentSortBy === 'price-low') {
      return (a.retailPrice || 0) - (b.retailPrice || 0);
    } else if (currentSortBy === 'price-high') {
      return (b.retailPrice || 0) - (a.retailPrice || 0);
    } else if (currentSortBy === 'rating') {
      // Sort by rating (mock data doesn't have ratings, so random)
      return 0;
    } else if (currentSortBy === 'newest') {
      // Sort by newest (mock data doesn't have dates, so random)
      return 0;
    } else {
      // Default sort by popularity (random for mock data)
      return 0;
    }
  });

  return (
    <>
      {/* Optimized spacing to position content properly below header */}
      <div className="pt-6">
        {/* Category Navigation - Positioned with proper spacing from header */}
        <CategoryNavigation
          onCategorySelect={handleCategorySelect}
          selectedCategory={selectedCategory}
          section="retail"
          onFilterHint={() => {
            // Optional: Add any additional filter hint behavior
            console.log('Filter hint triggered for category:', selectedCategory);
          }}
        />

        {/* Main Content - Remove border line and optimize spacing */}
        <section className="pt-4 pb-8">
          <div className="container">
            <div className="flex flex-col space-y-6">
              {/* Subcategory Navigation - Hidden to simplify categorization */}

              {/* Products Grid - 5 columns like Airbnb */}
              <div className={
                viewMode === 'grid'
                  ? 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6'
                  : 'space-y-4'
              }>
                {isLoading ? (
                  // Loading skeleton
                  Array.from({ length: 10 }).map((_, index) => (
                    <div key={index} className="animate-pulse">
                      <div className="aspect-square bg-muted rounded-xl mb-3"></div>
                      <div className="space-y-2">
                        <div className="h-4 bg-muted rounded w-3/4"></div>
                        <div className="h-3 bg-muted rounded w-1/2"></div>
                        <div className="h-4 bg-muted rounded w-1/3"></div>
                      </div>
                    </div>
                  ))
                ) : sortedProducts.length > 0 ? (
                  sortedProducts.map((product) => (
                    <AirbnbStyleProductCard
                      key={product.id}
                      product={product}
                      section="retail"
                    />
                  ))
                ) : (
                  <div className="col-span-full flex flex-col items-center justify-center py-16 text-center">
                    <div className="relative mb-6">
                      {/* Animated background circles */}
                      <div className="absolute inset-0 bg-gradient-to-br from-[#DC2626]/10 to-black/10 rounded-full animate-pulse"></div>
                      <div className="relative bg-white rounded-full p-6 shadow-lg border border-gray-100">
                        <div className="w-16 h-16 bg-gradient-to-br from-[#DC2626] to-[#DC2626]/80 rounded-full flex items-center justify-center">
                          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                          </svg>
                        </div>
                      </div>
                    </div>

                    <div className="max-w-md mx-auto">
                      <h3 className="text-2xl font-bold text-black mb-3">
                        No Products Found
                      </h3>
                      <p className="text-gray-600 text-lg leading-relaxed mb-8">
                        We couldn't find any products matching your current filters. Try adjusting your search criteria to discover more options.
                      </p>

                      <Button
                        variant="outline"
                        size="lg"
                        className="border-[#DC2626] text-[#DC2626] hover:bg-[#DC2626] hover:text-white transition-all duration-200 px-8 py-3 text-base font-medium shadow-sm"
                        onClick={() => {
                          // Clear product filters
                          setProductFilters({
                            priceRange: [0, 10000],
                            brands: [],
                            stockStatus: [],
                            ratings: [],
                            categories: [],
                            sortBy: 'popularity'
                          });
                          setSelectedSubcategory('');
                        }}
                      >
                        Clear Filters
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </section>
      </div>

      {/* Product Filter Modal - NEW SYSTEM */}
      <ProductFilterModal
        open={showProductFilterModal}
        onOpenChange={setShowProductFilterModal}
        onFiltersChange={handleProductFilterChange}
        initialFilters={productFilters}
        availableProducts={products}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
      />

      {/* Quotation Request Modal */}
      <QuotationRequestModal
        isOpen={showQuotationModal}
        onClose={() => setShowQuotationModal(false)}
        subcategory={selectedSubcategory || (selectedCategory === 'tyres' ? 'Tyres' : 'Brake Parts')}
        section="retail"
      />
    </>
  );
}

// Main component that provides the ProductFilterProvider
export default function MyVehicleParts() {
  return (
    <ProductFilterProvider>
      <MyVehiclePartsContent />
    </ProductFilterProvider>
  );
}
