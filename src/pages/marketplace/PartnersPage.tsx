import React, { useEffect, useState } from 'react';
import { useNavigate, Link, useLocation, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Building, Store, CheckCircle, XCircle, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { LanguageSelector } from '@/components/common/LanguageSelector';
import { useUser } from '@/contexts/UserContext';
import { Badge } from '@/components/ui/badge';
import { RoleAuthModal } from '@/components/marketplace/RoleAuthModal';

export default function PartnersPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const { userRole, isSupplier, isMerchant, supplierRole, merchantRole } = useUser();

  // Get the returnUrl from URL parameters if it exists
  const returnUrl = searchParams.get('returnUrl');

  // State for role-specific auth modals
  const [isSupplierAuthModalOpen, setIsSupplierAuthModalOpen] = React.useState(false);
  const [isMerchantAuthModalOpen, setIsMerchantAuthModalOpen] = React.useState(false);
  const [authMode, setAuthMode] = React.useState<'signup' | 'login'>('signup');

  // State to track authentication status for real-time updates
  const [supplierStatus, setSupplierStatus] = React.useState(supplierRole);
  const [merchantStatus, setMerchantStatus] = React.useState(merchantRole);

  // Update local state when user role changes
  useEffect(() => {
    setSupplierStatus(supplierRole);
    setMerchantStatus(merchantRole);
  }, [supplierRole, merchantRole]);

  // Handle authentication modal triggers
  const openSupplierAuthModal = (mode: 'signup' | 'login') => {
    // Don't open if already authenticated as supplier
    if (supplierStatus && mode === 'login') {
      navigate('/app/dashboard');
      return;
    }

    // Set the auth mode first to ensure the correct tab is active when the modal opens
    console.log(`Setting auth mode to: ${mode} for supplier modal`);
    setAuthMode(mode);
    setIsSupplierAuthModalOpen(true);
  };

  const openMerchantAuthModal = (mode: 'signup' | 'login') => {
    // Don't open if already authenticated as merchant
    if (merchantStatus && mode === 'login') {
      navigate('/app/dashboard');
      return;
    }

    // Set the auth mode first to ensure the correct tab is active when the modal opens
    console.log(`Setting auth mode to: ${mode} for merchant modal`);
    setAuthMode(mode);
    setIsMerchantAuthModalOpen(true);
  };

  // Listen for authentication success and update status in real-time
  useEffect(() => {
    const handleAuthSuccess = (event: Event) => {
      const customEvent = event as CustomEvent<{ role: string, mode: string }>;
      const { role } = customEvent.detail;

      console.log('Auth success event received for role:', role);

      // Update the appropriate role status in real-time
      if (role === 'supplier') {
        setSupplierStatus(true);
      } else if (role === 'merchant') {
        setMerchantStatus(true);
      }

      // Use a direct browser redirect for more reliable navigation
      const redirectUrl = returnUrl || '/app/dashboard';
      console.log('Performing hard redirect to:', redirectUrl);

      // Small timeout to ensure state updates are processed
      setTimeout(() => {
        window.location.href = redirectUrl;
      }, 100);
    };

    document.addEventListener('auth-success', handleAuthSuccess);

    return () => {
      document.removeEventListener('auth-success', handleAuthSuccess);
    };
  }, [returnUrl]);

  return (
    <div className="h-screen flex flex-col bg-white">
      {/* Header with logo and language selector */}
      <header className="bg-white shadow-sm py-4 px-6">
        <div className="container mx-auto flex justify-between items-center">
          <a href="/" className="flex items-center select-none flex-shrink-0" draggable="false">
            <div
              className="relative overflow-visible flex items-center"
              style={{
                pointerEvents: 'none',
                userSelect: 'none',
                marginLeft: '-0.25rem'
              }}
            >
              <div className="relative" style={{ height: '3rem', width: '5rem', overflow: 'visible' }}>
                <img
                  src="/images/KALIXLOGO.png"
                  alt="KALIX Logo"
                  className="w-auto h-12 md:h-20 object-contain absolute top-1/2 left-0 transform -translate-y-1/2"
                  style={{
                    filter: 'drop-shadow(0px 2px 3px rgba(0, 0, 0, 0.12))',
                    transform: 'translateY(-50%) scale(1.35)',
                    transformOrigin: 'left center',
                    maxHeight: '140%'
                  }}
                  draggable="false"
                  onDragStart={(e) => e.preventDefault()}
                />
              </div>
            </div>
          </a>
          <LanguageSelector />
        </div>
      </header>

      {/* Main content */}
      <main className="flex-1 flex flex-col md:flex-row">
        {/* Supplier & Manufacturer Section - Temporarily covered with image */}
        <section className="flex-1 flex flex-col items-center justify-center p-8 bg-gradient-to-br from-[#ffd9a3]/30 to-[#ffd9a3]/10 relative" data-role="supplier">
          {/* Cover image overlay */}
          <div className="absolute inset-0 z-10 flex items-center justify-center">
            <img
              src="/dist/images/auto-parts-store-seller.avif"
              alt="Auto Parts Store Seller"
              className="w-full h-full object-cover rounded-lg shadow-lg"
              style={{
                objectFit: 'cover',
                objectPosition: 'center'
              }}
            />
            {/* Optional overlay for better text readability if needed */}
            <div className="absolute inset-0 bg-black/20 rounded-lg"></div>
          </div>
          {/* Original content hidden behind overlay - preserved for future reactivation */}
          <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 border border-[#DC2626]/20 relative z-0 opacity-0 pointer-events-none">
            <div className="flex items-center justify-between mb-6">
              <div className="w-16 h-16 rounded-full bg-[#DC2626]/10 flex items-center justify-center">
                <Building className="h-8 w-8 text-[#DC2626]" />
              </div>

              {/* Account status indicator */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">{t('auth.supplierAccountStatus')}:</span>
                {supplierStatus ? (
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" />
                    {t('auth.active')}
                  </Badge>
                ) : (
                  <Badge variant="outline" className="bg-gray-50 text-gray-500 border-gray-200 flex items-center gap-1">
                    <XCircle className="h-3 w-3" />
                    {t('auth.inactive')}
                  </Badge>
                )}
              </div>
            </div>

            <h2 className="text-2xl font-bold text-center text-[#071c44] mb-4">
              {t('auth.supplierPartner')}
            </h2>

            <p className="text-center text-gray-600 mb-6">
              {t('auth.supplierDescription')}
            </p>

            <div className="space-y-3">
              {supplierStatus ? (
                <Button
                  className="w-full bg-[#DC2626] hover:bg-[#DC2626]/90 text-white flex items-center justify-center gap-2"
                  onClick={() => {
                    console.log('Continue to account button clicked, navigating to:', returnUrl || '/app/dashboard');
                    // Use window.location for a hard redirect instead of React Router
                    window.location.href = returnUrl || '/app/dashboard';
                  }}
                >
                  {t('auth.continueToAccount')}
                  <ArrowRight className="h-4 w-4" />
                </Button>
              ) : (
                <>
                  <Button
                    className="w-full bg-[#DC2626] hover:bg-[#DC2626]/90 text-white"
                    onClick={() => openSupplierAuthModal('signup')}
                    aria-label={t('auth.signUp')}
                  >
                    {t('auth.signUp')}
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full border-[#DC2626] text-[#DC2626] hover:bg-[#DC2626]/10"
                    onClick={() => openSupplierAuthModal('login')}
                    aria-label={t('auth.login')}
                  >
                    {t('auth.login')}
                  </Button>
                </>
              )}
            </div>
          </div>
        </section>

        {/* Divider for mobile */}
        <div className="h-px w-full bg-gray-200 md:hidden"></div>

        {/* Divider for desktop */}
        <div className="hidden md:block w-px h-full bg-gray-200"></div>

        {/* Merchant Retailer Section */}
        <section className="flex-1 flex flex-col items-center justify-center p-8 bg-gradient-to-br from-[#071c44]/5 to-[#071c44]/10" data-role="merchant">
          <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 border border-[#071c44]/20">
            <div className="flex items-center justify-between mb-6">
              <div className="w-16 h-16 rounded-full bg-[#071c44]/10 flex items-center justify-center">
                <Store className="h-8 w-8 text-[#071c44]" />
              </div>

              {/* Account status indicator */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">{t('auth.merchantAccountStatus')}:</span>
                {merchantStatus ? (
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" />
                    {t('auth.active')}
                  </Badge>
                ) : (
                  <Badge variant="outline" className="bg-gray-50 text-gray-500 border-gray-200 flex items-center gap-1">
                    <XCircle className="h-3 w-3" />
                    {t('auth.inactive')}
                  </Badge>
                )}
              </div>
            </div>

            <h2 className="text-2xl font-bold text-center text-[#071c44] mb-4">
              {t('auth.merchantPartner')}
            </h2>

            <p className="text-center text-gray-600 mb-6">
              {t('auth.merchantDescription')}
            </p>

            <div className="space-y-3">
              {merchantStatus ? (
                <Button
                  className="w-full bg-[#071c44] hover:bg-[#071c44]/90 text-white flex items-center justify-center gap-2"
                  onClick={() => {
                    console.log('Continue to account button clicked, navigating to:', returnUrl || '/app/dashboard');
                    // Use window.location for a hard redirect instead of React Router
                    window.location.href = returnUrl || '/app/dashboard';
                  }}
                >
                  {t('auth.continueToAccount')}
                  <ArrowRight className="h-4 w-4" />
                </Button>
              ) : (
                <>
                  <Button
                    className="w-full bg-[#071c44] hover:bg-[#071c44]/90 text-white"
                    onClick={() => openMerchantAuthModal('signup')}
                    aria-label={t('auth.signUp')}
                  >
                    {t('auth.signUp')}
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full border-[#071c44] text-[#071c44] hover:bg-[#071c44]/10"
                    onClick={() => openMerchantAuthModal('login')}
                    aria-label={t('auth.login')}
                  >
                    {t('auth.login')}
                  </Button>
                </>
              )}
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-white py-4 px-6 border-t">
        <div className="container mx-auto text-center text-sm text-gray-500">
          © {new Date().getFullYear()} AROUZ MARKET. {t('public.allRightsReserved')}
        </div>
      </footer>

      {/* Role-specific Authentication Modals */}
      <RoleAuthModal
        isOpen={isSupplierAuthModalOpen}
        onClose={() => setIsSupplierAuthModalOpen(false)}
        role="supplier"
        initialMode={authMode}
      />

      <RoleAuthModal
        isOpen={isMerchantAuthModalOpen}
        onClose={() => setIsMerchantAuthModalOpen(false)}
        role="merchant"
        initialMode={authMode}
      />
    </div>
  );
}
