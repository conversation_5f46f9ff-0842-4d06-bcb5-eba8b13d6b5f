{"general": {"appName": "AROUZ MARKET", "dashboard": "Tableau <PERSON>", "welcome": "<PERSON> retour, {{name}}. Voici ce qui se passe avec vos produits."}, "navigation": {"dashboard": "Tableau <PERSON>", "products": "Produits", "productsTable": "Grille de Données Produits", "allProducts": "Tous les Produits", "addProduct": "Ajouter un Produit", "orders": "Commandes", "shipments": "Expéditions", "categories": "Catégories", "customers": "Clients", "compatibilityHub": "Hub de Compatibilité", "settings": "Paramètres", "logout": "Déconnexion"}, "dashboard": {"totalProducts": "Total des Produits", "pendingApprovals": "Approbations en Attente", "outOfStock": "<PERSON><PERSON><PERSON> <PERSON>", "recentlyViewed": "Récemment Consultés", "recentProducts": "Produits Récents", "latestAddedProducts": "Vos derniers produits ajoutés", "viewAll": "Voir Tout", "productPerformance": "Performance des Produits", "topViewedProducts": "Produits les plus consultés ce mois-ci"}, "products": {"addProduct": "Ajouter un Produit", "importProducts": "Importer des Produits", "importFromFile": "Importer des produits à partir d'un fichier CSV ou Excel", "uploadFile": "Télécharger le Fichier", "reviewAndFetch": "Réviser et Récupérer", "completeImport": "Terminer l'Importation", "dragAndDropFile": "Glissez et déposez votre fichier ici ou cliquez pour parcourir", "supportedFormats": "Formats supportés : CSV, Excel (.xlsx, .xls)", "browseFiles": "Parcourir les Fichiers", "needTemplate": "Besoin d'un modèle ?", "downloadTemplate": "Télécharger le Modèle", "templateDescription": "Téléchargez un fichier modèle avec les en-têtes corrects pour importer des produits.", "uploadAndReview": "Télécharger et Réviser", "uploadYourProductFile": "Téléchargez votre fichier de produits", "dragAndDropOrClick": "Glissez et déposez votre fichier ici ou cliquez pour parcourir", "maxFileSize": "Taille max : 500 Mo", "intelligentColumnMapping": "Mappage Intelligent des Colonnes", "automaticallyAnalyze": "Analyser automatiquement les en-têtes de colonnes et le contenu pour un mappage précis des données", "enableSmartMapping": "Activer le Mappage Intelligent", "advancedAnalysisActive": "Service d'Analyse Avancée Actif", "intelligentColumnAnalysis": "Analyse intelligente des en-têtes de colonnes", "contentPatternRecognition": "Reconnaissance des motifs de contenu", "automaticFallback": "Retour automatique au mappage manuel", "analyzingColumnStructure": "Analyse de la structure des colonnes et du contenu...", "mayTakeFewMoments": "<PERSON><PERSON> peut prendre quelques instants pour les gros fichiers", "readingFile": "Lecture du fichier...", "analyzingStructure": "Analyse de la structure...", "processingData": "Traitement des données...", "finalizing": "Finalisation...", "parsingFileContent": "Analyse du contenu du fichier et validation du format", "detectingColumns": "Détection des colonnes et des motifs de données", "mappingDataToFields": "Mappage des données aux champs de produit", "preparingDataForReview": "Préparation des données pour révision", "reading": "Lecture...", "analyzing": "Analyse...", "processing": "Traitement...", "totalRows": "Total des Lignes", "articleNumbers": "Numéros d'Article", "needFetch": "Besoin de Récupération", "duplicates": "Doublons", "fetchingFromTecDoc": "Récupération des données produit depuis TecDoc...", "completed": "terminé", "fetchMissingData": "Récupérer les Données Manquantes", "backToUpload": "Retour au Téléchargement", "continueToComplete": "Continuer vers Te<PERSON>iner", "readyToImport": "prêt à importer", "requireAttention": "nécessite attention", "tecDocEnhanced": "<PERSON><PERSON><PERSON><PERSON>", "manualEntry": "<PERSON><PERSON>", "issuesRequiringAttention": "Problèmes Nécessitant une Attention", "backToReview": "Retour à la Révision", "importSuccess": "{{count}} produits importés avec succès", "fileUploadSuccess": "<PERSON><PERSON>er téléchargé avec succès. Trouvé {{total}} enregistrements avec {{articleNumbers}} Numéros d'Article.", "fetchDataSuccess": "Données récupérées pour {{count}} sur {{total}} produits", "noArticleNumbers": "Aucun Numéro d'Article trouvé pour la récupération de données", "articleNumberRequired": "Le Numéro d'Article est requis pour la récupération de données", "dataFetchedSuccess": "Données récupérées avec succès pour {{articleNumber}}", "noDataFound": "Aucune donnée trouvée pour {{articleNumber}}. Veuillez remplir manuellement.", "fetchDataFailed": "Échec de la récupération des données produit", "export": "Exporter", "search": "Rechercher des produits...", "category": "<PERSON><PERSON><PERSON><PERSON>", "allCategories": "Toutes les Catégories", "status": "Statut", "allStatus": "Tous les Statuts", "moreFilters": "Plus de Filtres", "engineParts": "<PERSON><PERSON><PERSON> Mo<PERSON>ur", "brakeSystem": "Système de Freinage", "suspension": "Suspension", "electrical": "Électrique", "bodyParts": "Pièces de Carrosserie", "published": "<PERSON><PERSON><PERSON>", "draft": "Brouillon", "outOfStock": "<PERSON><PERSON><PERSON> <PERSON>", "edit": "Modifier", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "feature": "Mettre en Avant", "stock": "Stock", "productLibrary": "Bibliothèque de Produits", "libraryDescription": "Gérez votre catalogue de produits avec filtrage avancé et actions groupées", "name": "Nom du Produit", "sku": "SKU", "barcode": "Code-barres", "price": "Prix", "manufacturer": "Fabricant", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour le", "actions": "Actions", "viewHistory": "Voir l'historique", "duplicateSuccess": "Produit dupli<PERSON><PERSON> avec succès", "deleteSuccess": "Produit supprimé avec succès", "editSuccess": "Produit modifié avec succès", "bulkDeleteSuccess": "{{count}} produits supprimés avec succès", "exportSuccess": "{{count}} produits exportés avec succès", "bulkStatusSuccess": "Statut mis à jour pour {{count}} produits", "noProductsSelected": "Aucun produit sélectionné", "selected": "{{count}} sélectionnés", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "noProductsFound": "Aucun produit trouvé"}, "productsDataGrid": {"title": "Bibliothèque de Produits", "description": "Gérez votre catalogue de produits avec filtrage avancé et actions groupées", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "columns": "Colonnes", "import": "Importer", "export": "Exporter", "search": "Rechercher des produits...", "allCategories": "Toutes les Catégories", "allStatus": "Tous les Statuts", "productName": "Nom du Produit", "category": "<PERSON><PERSON><PERSON><PERSON>", "price": "Prix", "stock": "Stock", "status": "Statut", "updatedAt": "Mis à jour le"}, "categories": {"allProducts": "Tous les Produits", "addCategory": "A<PERSON>ter une Catégorie", "addedSuccessfully": "Catégorie a<PERSON> avec succès", "notFound": "<PERSON><PERSON><PERSON><PERSON> introuvable", "edit": "Modifier la Catégorie", "delete": "Supprimer la Catégorie", "createCategory": "<PERSON><PERSON>er une Catégorie", "categoryName": "Nom de la Catégorie", "categoryDescription": "Description de la Catégorie", "categoryImage": "Image de la Catégorie", "uploadImage": "Télécharger une Image", "categoryDetails": "<PERSON>é<PERSON> de la Catégorie", "saveCategory": "Enregistrer la Catégorie"}, "actions": {"save": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "add": "Ajouter", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>"}, "language": {"arabic": "العربية", "english": "English", "french": "Français", "selectLanguage": "<PERSON><PERSON>"}, "roles": {"manufacturer": "Fournisseur & Fabricant", "merchant": "<PERSON><PERSON><PERSON><PERSON>", "consumer": "Consommateur", "distribution": "Distribution", "manufacturerDescription": "<PERSON><PERSON><PERSON> les produits, l'inventaire et les prix de gros pour les fabricants et fournisseurs", "merchantDescription": "Vendre des produits directement aux consommateurs avec des prix de détail et gestion de magasin", "consumerDescription": "Parcourir et acheter des produits", "distributionDescription": "Gérer l'expédition et la logistique"}, "auth": {"loginOrSignUp": "Se connecter ou S'inscrire", "welcomeToArouz": "Bienvenue sur AROUZ MARKET", "countryRegion": "Pays/Région", "phoneNumber": "Numéro de téléphone", "phoneFormat": "Entrez votre numéro de mobile algérien à 9 chiffres (ex., XX XXX XX XX)", "optionalPhoneVerification": "Vérification du Téléphone", "requiredPhoneVerification": "Vérification du Téléphone (Obligatoire)", "privacyNotice": "Nous vous appellerons ou vous enverrons un SMS pour confirmer votre numéro. Des frais standard de message et de données peuvent s'appliquer.", "privacyPolicy": "Politique de confidentialité", "continue": "<PERSON><PERSON><PERSON>", "or": "ou", "continueWithGoogle": "Continuer avec Google", "continueWithApple": "Continuer avec Apple", "continueWithEmail": "Continuer avec email", "continueWithFacebook": "Continuer avec Facebook", "becomeSupplier": "<PERSON><PERSON><PERSON>", "joinAsMerchant": "Rejoindre en tant que Commerçant", "sellWithUs": "Si vous souhaitez vendre vos produits avec nous :", "supplierTooltip": "Inscrivez-vous pour vendre des produits en gros aux détaillants.", "merchantTooltip": "Inscrivez-vous pour vendre des produits directement aux consommateurs.", "supplierSignUp": "Inscription Fournisseur & Fabricant", "merchantSignUp": "Inscription Commerçant Détaillant", "supplierLogin": "Connexion Fournisseur & Fabricant", "merchantLogin": "Connexion Commerçant Détaillant", "companyName": "Nom de l'entreprise", "enterCompanyName": "Entrez le nom de votre entreprise", "taxId": "Numéro fiscal", "enterTaxId": "Entrez votre numéro fiscal", "storeName": "Nom du magasin", "enterStoreName": "Entrez le nom de votre magasin", "storeAddress": "<PERSON><PERSON><PERSON> <PERSON> ma<PERSON>in", "enterStoreAddress": "Entrez l'adresse de votre magasin", "signUp": "S'inscrire", "login": "Se Connecter", "alreadyHaveAccount": "Vous avez déjà un compte ?", "dontHaveAccount": "Vous n'avez pas de compte ?", "otpCode": "Code de vérification", "otpDescription": "Entrez le code de vérification envoyé à votre téléphone", "verify": "Vérifier", "verifying": "Vérification en cours...", "processing": "Traitement en cours...", "verifyOtp": "Vérifiez votre téléphone", "didntReceiveCode": "Vous n'avez pas reçu de code ?", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "registrationSuccess": "Inscription réussie !", "loginSuccess": "Connexion réussie !", "verificationSuccess": "Vérification réussie !", "registrationFailed": "Échec de l'inscription", "loginFailed": "Échec de la connexion", "verificationFailed": "Échec de la vérification", "verificationEmailSent": "Un email de confirmation a été envoyé à votre boîte de réception", "checkYourEmail": "Vérifiez votre email", "confirmEmailInstructions": "Veuillez vérifier votre email et cliquer sur le lien de confirmation pour activer votre compte", "emailVerificationRequired": "Important : V<PERSON><PERSON> d'Email Requise", "emailSentTo": "Nous avons envoyé un email de confirmation à", "verificationLinkInstructions": "Veuillez vérifier votre boîte de réception et cliquer sur le lien de vérification pour activer votre compte", "whatToDoNext": "Que faire ensuite", "checkInbox": "Vérifiez votre boîte de réception (et dossier spam)", "openEmail": "Ouvrez l'email de AROUZ MARKET", "clickVerificationLink": "Cliquez sur le lien de vérification pour activer votre compte", "loginAfterVerification": "Une fois vérifié, vous pourrez vous connecter pour accéder à votre compte", "gotIt": "<PERSON><PERSON><PERSON>", "verifyingAccount": "Vérification de votre compte", "pleaseWait": "Veuillez patienter pendant que nous vérifions votre compte", "accountVerified": "Compte vérifié avec succès !", "redirectingToDashboard": "Vous serez redirigé vers votre tableau de bord dans un instant", "userNotFound": "Utilisateur non trouvé", "profileNotFound": "Profil non trouvé", "unexpectedError": "Une erreur inattendue s'est produite", "returnToPartners": "Retour aux Partenaires", "alreadyVerified": "Ce compte a déjà été vérifié. Veuillez vous connecter à la place.", "linkExpired": "Le lien de vérification a expiré ou est invalide. Veuillez en demander un nouveau.", "emailAlreadyRegistered": "Cet email est déjà enregistré. Veuillez utiliser l'option de connexion à la place.", "supplierPartner": "Fournisseur & Fabricant", "supplierDescription": "Rejoignez en tant que Fournisseur & Fabricant pour vendre vos produits en gros aux détaillants à travers l'Algérie. Accédez à des outils B2B puissants et développez votre réseau de distribution.", "merchantPartner": "<PERSON><PERSON><PERSON><PERSON>", "merchantDescription": "Rejoignez en tant que Commerçant Détaillant pour vendre des produits directement aux consommateurs. Créez votre boutique en ligne et atteignez des clients dans toute l'Algérie avec notre marketplace B2C.", "roleConflictTitle": "Conflit de Rôle Détecté", "roleConflictDescription": "Vous ne pouvez pas être connecté simultanément en tant que Fournisseur & Fabricant et Commerçant Détaillant. Veuillez vous déconnecter de votre rôle actuel pour continuer.", "logoutAndContinue": "Déconnexion et Continuer", "roleLogoutSuccess": "Déconnexion du Rôle Réussie", "roleLogoutSuccessDescription": "Vous avez été déconnecté de votre rôle précédent.", "emailRoleMismatchTitle": "Incompatibilité de Rôle de Compte", "emailRoleMismatchSupplier": "Cet email est enregistré comme compte Fournisseur & Fabricant. Veuillez utiliser la section de connexion Fournisseur & Fabricant.", "emailRoleMismatchMerchant": "Cet email est enregistré comme compte Commerçant Détaillant. Veuillez utiliser la section de connexion Commerçant Détaillant.", "sessionConflictTitle": "Conflit de Session Détecté", "sessionConflictSupplierActive": "Vous êtes actuellement connecté en tant que Fournisseur & Fabricant. Veuillez vous déconnecter de votre compte Fournisseur avant de vous connecter en tant que Commerçant Détaillant.", "sessionConflictMerchantActive": "Vous êtes actuellement connecté en tant que Commerçant Détaillant. Veuillez vous déconnecter de votre compte Commerçant avant de vous connecter en tant que Fournisseur & Fabricant.", "logoutFromCurrentRole": "Se Déconnecter du Rôle Actuel", "switchToCorrectLogin": "Basculer vers la Connexion Correcte", "stayLoggedIn": "<PERSON><PERSON>", "authenticationBlocked": "Authentification Bloquée", "authenticationBlockedDescription": "Veuillez résoudre le conflit de rôle pour continuer.", "loginRequired": "Connexion Requise", "loginRequiredDescription": "Veuillez vous connecter pour ajouter des articles à votre liste de souhaits", "logout": "Se Déconnecter", "loggedOut": "Déconnecté", "loggedOutDescription": "Vous avez été déconnecté avec succès"}, "marketplace": {"navigation": "Navigation", "myVehicleParts": "Pièces Pour Mon Véhicule", "wholesaleOffers": "Offres en Gros", "wholesaleOffersDescription": "Découvrez les offres en gros et les offres spéciales des fournisseurs et fabricants", "noWholesaleProductsFound": "Aucun produit en gros trouvé", "tryDifferentFiltersWholesale": "Essayez d'ajuster vos filtres ou parcourez différentes catégories pour trouver des offres en gros", "account": "<PERSON><PERSON><PERSON>", "cart": "<PERSON><PERSON>", "item": "article", "items": "articles", "close": "<PERSON><PERSON><PERSON>", "tyres": "Pneus", "allOtherParts": "Moteur de Recherche de Pièces", "comingSoon": "Bientôt Disponible", "underDevelopment": "Cette fonctionnalité est en cours de développement et sera bientôt disponible !", "tyreWidth": "<PERSON><PERSON> du Pneu", "aspectRatio": "Rapport d'Aspect", "rimDiameter": "<PERSON><PERSON><PERSON><PERSON>", "season": "<PERSON><PERSON>", "selectWidth": "Sélectionner la Largeur", "selectRatio": "Sélectionner le Rapport", "selectDiameter": "Sélectionner le Diamètre", "selectSeason": "Sélectionner la Saison", "summer": "<PERSON><PERSON>", "winter": "Hiver", "allSeason": "<PERSON><PERSON> Sai<PERSON>", "deliverTo": "<PERSON><PERSON>", "selectLocation": "Sélectionner l'Emplacement", "chooseYourLocation": "Choisissez Votre Emplacement", "enterDeliveryLocation": "Entrez l'Adresse de Livraison", "enterLocation": "Entrez votre emplacement", "currentLocation": "Position Actuelle", "searchLocation": "Rechercher un emplacement", "locationPermissionDenied": "Accès à la localisation refusé. Veuillez activer les services de localisation.", "locationUnavailable": "Les informations de localisation ne sont pas disponibles.", "locationTimeout": "<PERSON><PERSON>lai d'attente de la demande de localisation dépassé.", "gettingLocation": "Obtention de votre position...", "selectOnMap": "Sélectionner sur la Carte", "dragToAdjust": "Faites glisser le marqueur pour ajuster l'emplacement", "searchTyres": "Rechercher des Pneus", "searchResults": "Résultats de Recherche", "filters": "Filtres", "resetAll": "Réinitialiser <PERSON>ut", "priceRange": "Fourchette de Prix", "brand": "Marque", "cartEmpty": "Votre panier est vide", "addItemsToCart": "Ajoutez des articles à votre panier pour les voir ici", "continueShopping": "Continuer les Achats", "quantity": "Quantité", "removeItem": "Supprimer l'Article", "subtotal": "Sous-total", "checkout": "Commander", "addToCart": "<PERSON><PERSON><PERSON> au Panier", "addToWishlist": "Ajouter aux Favoris", "removeFromWishlist": "Retirer des Favoris", "addedToWishlist": "Ajouté aux Favoris", "removedFromWishlist": "Re<PERSON><PERSON> Favoris", "addedToWishlistDescription": "Produit sauvegardé dans vos favoris", "removedFromWishlistDescription": "Produit retiré de vos favoris", "wishlistError": "<PERSON><PERSON><PERSON> <PERSON> Liste de Souhaits", "wishlistErrorDescription": "Échec de la mise à jour de la liste de souhaits. Veuillez réessayer.", "writeReview": "Écrire un Avis", "customerReviews": "Avis <PERSON>", "noReviewsYet": "Aucun Avis Pour l'Instant", "beFirstToReview": "Soyez le premier à partager votre expérience avec ce produit!", "writeFirstReview": "Écrire le Premier Avis", "reviewSubmitted": "<PERSON><PERSON>", "reviewSubmittedDescription": "Merci pour votre avis! Il sera publié sous peu.", "reviewAddedSuccessfully": "<PERSON><PERSON> avec Succès", "reviewAddedSuccessfullyDescription": "Votre avis a été ajouté et est maintenant visible aux autres clients.", "outOfStock": "<PERSON><PERSON><PERSON> <PERSON>", "inStock": "En Stock", "piece": "pi<PERSON><PERSON>", "useFiltersToRefine": "Utilisez les filtres pour trouver des produits spécifiques", "allCategories": "Toutes les Catégories", "allCategoriesDesc": "Parcourir tous les produits disponibles", "tyresDesc": "Trouvez les pneus parfaits pour votre véhicule", "brakePartsDesc": "Composants et pièces de frein de qualité", "heroTitle": "Trouvez les Pièces Parfaites pour Votre Véhicule", "heroSubtitle": "Parcourez notre vaste catalogue de pièces auto et accessoires", "findPartsForVehicle": "Trouver des Pièces pour Mon Véhicule", "browseWholesale": "Parcourir les Offres en Gros", "mainCategories": "Catégories Principales", "vehiclePartsDescription": "Trouvez des pièces spécifiquement conçues pour votre véhicule avec notre système de correspondance intelligent", "findParts": "Trouver des Pièces", "wholesaleDescription": "Explorez les offres en gros et les offres spéciales pour les commerçants et les détaillants", "browseOffers": "Parcourir les Offres", "whyChooseUs": "Pourquoi Choisir AROUZ MARKET", "whyChooseUsDescription": "Nous offrons une expérience d'achat fluide avec des produits de qualité et un service exceptionnel", "easySearch": "Recherche Facile", "easySearchDescription": "Trouvez les bonnes pièces pour votre véhicule en quelques clics", "fastDelivery": "Livraison Rapide", "fastDeliveryDescription": "<PERSON><PERSON>vez vos pièces rapidement à votre emplacement", "qualityGuarantee": "Garantie de Qualité", "qualityGuaranteeDescription": "Tous nos produits répondent ou dépassent les spécifications OEM", "support": "Support 24/7", "supportDescription": "Notre équipe de service client est toujours prête à vous aider", "readyToShop": "<PERSON><PERSON><PERSON><PERSON>?", "readyToShopDescription": "Commencez à parcourir notre vaste catalogue de pièces auto et accessoires", "startShopping": "Commencer les Achats", "products": "produits", "sortBy": "Trier par", "popularity": "Popularité", "priceLowToHigh": "Prix: <PERSON><PERSON><PERSON><PERSON>", "priceHighToLow": "Prix: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minimumOrderQuantity": "Quantité Minimum de Commande", "highestRated": "<PERSON><PERSON>", "gridView": "Vue en Grille", "listView": "Vue en Liste", "remove": "<PERSON><PERSON><PERSON><PERSON>", "clearFilters": "Effacer les Filtres", "selectBrand": "Sélectionner la Marque", "selectModel": "Sélectionner le Modèle", "selectEngine": "Sélectionner le Moteur", "searchBrands": "Rechercher des marques", "searchModels": "Rechercher des modèles", "searchEngines": "Rechercher des moteurs", "searchForBrand": "Rechercher une marque", "searchForModel": "Rechercher un modèle", "searchForEngine": "Rechercher un moteur", "noBrandsFound": "Aucune marque trouvée", "noModelsFound": "<PERSON><PERSON><PERSON> mod<PERSON>le trouvé", "noEnginesFound": "<PERSON>cun moteur trouvé", "searchParts": "Rechercher des Pièces", "searchAllProducts": "Rechercher des produits...", "startYourSearch": "Commencez votre recherche", "searchProducts": "Rechercher des Produits", "searchText": "Texte de Recherche", "searching": "Recherche en cours...", "suggestions": "Suggestions", "recentSearches": "Recherches Récentes", "popularSearches": "Recherches Populaires", "noSuggestions": "<PERSON><PERSON><PERSON> trouvée", "vehicleInfoRequired": "Informations Véhicule Requises", "pleaseSelectVehicle": "Veuillez sélectionner les détails de votre véhicule pour les recherches de pneus", "searchResultsCount": "{{count}} résultats trouvés pour \"{{query}}\" en {{time}}ms", "searchFor": "Recherche pour \"{{query}}\"", "noSearchQuery": "<PERSON><PERSON><PERSON>", "enterSearchQuery": "Veuillez entrer une requête de recherche pour trouver des produits", "backToMarketplace": "Retour au Marché", "searchError": "<PERSON><PERSON><PERSON>", "searchErrorDescription": "Une erreur s'est produite lors de la recherche. Veuillez réessayer.", "relatedSearches": "Recherches Associées", "clearAll": "<PERSON><PERSON>", "activeFilters": "Filtres Actifs", "min": "Min", "max": "Max", "inStockOnly": "En Stock Seulement", "showingResults": "Affichage {{start}}-{{end}} sur {{total}} résultats", "relevance": "Pertinence", "name": "Nom", "newest": "Plus Récent", "searchingDescription": "Recherche des meilleurs produits pour vous...", "noResults": "Aucun Résultat Trouvé", "noResultsDescription": "Nous n'avons trouvé aucun produit correspondant à \"{{query}}\"", "tryThese": "Essayez ces suggestions:", "checkSpelling": "Vérifiez l'orthographe", "useFewerWords": "Utilisez moins de mots-clés", "tryDifferentTerms": "Essayez des termes différents", "removeFilters": "Supprimez certains filtres", "myWishlist": "<PERSON> <PERSON><PERSON>", "myReviews": "<PERSON><PERSON>", "messages": "Messages", "languages": "<PERSON><PERSON>", "helpCenter": "Centre d'Aide", "profile": "Profil", "tyreParametersGuide": "Guide des Paramètres de Pneus", "learnMore": "En Savoir Plus", "tyreParametersExplained": "Comprendre les Spécifications des Pneus", "tyreParametersDescription": "Les spécifications des pneus sont des codes standardisés qui indiquent les dimensions et capacités exactes d'un pneu.", "tyreWidthDescription": "Largeur du pneu en millimètres", "aspectRatioDescription": "Hauteur en pourcentage de la largeur", "constructionTypeDescription": "Type de construction radiale", "rimDiameterDescription": "Diamètre de la jante en pouces", "loadIndexDescription": "Indice de charge maximale", "speedRatingDescription": "Indice de vitesse maximale", "tyreParametersHelp": "Utilisez ce guide pour vous aider à sélectionner les bonnes spécifications de pneus pour votre véhicule.", "categories": {"allCategories": "Toutes les Catégories", "tyres": "Pneus et Produits Connexes", "brakes": "Pièces et Systèmes de Freinage", "filters": "Filtres", "oilsFluids": "<PERSON>les et Fluides", "engine": "<PERSON><PERSON><PERSON>", "windowCleaning": "Nettoyage des Vitres", "glowPlugIgnition": "Bougie de Préchauffage et Allumage", "wishbonesSuspension": "Triangles et Suspension", "electricalSystems": "Systèmes Électriques", "ignition": "Allumage", "exterior": "Extérieur"}}, "checkout": {"title": "Commande", "secureCheckout": "Commande Sécurisée", "orderSummary": "Résumé de la Commande", "subtotal": "Sous-total", "shipping": "<PERSON><PERSON><PERSON>", "arouzFee": "Frais AROUZ", "total": "Total", "calculatedAtCheckout": "Calculé à la commande", "cashOnDelivery": "Paiement à la Livraison", "storePickup": "Retrait en Magasin", "orderCreated": "Commande Créée avec Succès", "orderNumber": "Numéro de Commande", "orderFailed": "Échec de la Création de Commande", "orderFailedDescription": "Une erreur s'est produite lors de la création de votre commande. Veuillez réessayer.", "unexpectedError": "Une erreur inattendue s'est produite. Veuillez réessayer.", "authRequired": "Authentification Requise", "pleaseSignIn": "Veuillez vous connecter pour continuer la commande", "pleaseSignInFirst": "Veuillez d'abord vous connecter pour continuer", "signInToContinue": "Connectez-vous pour continuer la commande", "completeAuthentication": "Veuillez compléter la vérification téléphonique pour continuer", "deliveryRequired": "<PERSON><PERSON><PERSON> <PERSON>", "selectDeliveryLocation": "Veuillez sélectionner un lieu de livraison pour continuer", "paymentRequired": "Méthode de Paiement Requise", "selectPaymentMethod": "Veuillez sélectionner une méthode de paiement pour continuer", "step1": {"title": "Vérification Téléphonique", "description": "Vérifiez votre numéro de téléphone pour continuer la commande", "authRequired": "Authentification Requise", "authDescription": "Veuillez vérifier votre numéro de téléphone pour continuer votre commande", "verifyPhone": "Vérifier le Numéro de Téléphone", "securityFeatures": "Fonctionnalités de Sécurité", "feature1": "Vérification SMS sécurisée", "feature2": "Aucun mot de passe requis", "feature3": "Processus rapide et facile", "authSuccess": "Authentification réussie", "welcomeBack": "Bon retour ! Vous pouvez maintenant continuer votre commande.", "consumer": "Consommateur", "continue": "<PERSON><PERSON><PERSON>"}, "step2": {"title": "<PERSON><PERSON><PERSON>", "description": "Choisissez votre lieu de livraison", "selectWilaya": "Sélectionner la Wilaya", "searchWilaya": "Rechercher une wilaya...", "selectLocation": "Sélectionner l'emplacement sur la carte", "confirmLocation": "Confirmer l'emplacement", "deliveryAddress": "<PERSON><PERSON><PERSON>", "specialInstructions": "Instructions spéciales (optionnel)", "instructionsPlaceholder": "Toute instruction spéciale pour la livraison...", "continue": "<PERSON><PERSON><PERSON>"}, "step3": {"title": "Méthode de Paiement", "description": "Choisissez comment vous souhaitez payer votre commande", "codNote": "Paiement en Espèces Requis", "codDescription": "Ayez la monnaie exacte prête pour le livreur", "pickupNote": "Emplacement du Magasin", "pickupDescription": "Vous recevrez les détails de retrait après confirmation de commande", "securityTitle": "Paie<PERSON>", "security1": "Toutes les transactions sont sécurisées et cryptées", "security2": "Aucune information de paiement stockée", "security3": "Politique de remboursement complète disponible", "selectedMethod": "Méthode de Paiement Sélectionnée", "comingSoon": "Bientôt Disponible", "futurePayments": "Les options de paiement en ligne seront bientôt disponibles", "continue": "<PERSON><PERSON><PERSON>"}, "step4": {"title": "Confirmation de Commande", "description": "Vérifiez les détails de votre commande avant confirmation", "customerInfo": "Informations Client", "name": "Nom", "phone": "Téléphone", "consumer": "Consommateur", "deliveryInfo": "<PERSON><PERSON> de Livraison", "wilaya": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "instructions": "Instructions Spéciales", "viewOnMaps": "Voir sur les Cartes", "paymentMethod": "Méthode de Paiement", "cashOnDelivery": "Paiement à la Livraison", "express24h": "Express 24h", "storePickup": "Retrait en Magasin & Paiement", "noDeliveryFee": "<PERSON><PERSON><PERSON>", "shippingOrigins": "Origines d'Expédition", "estimatedLocation": "Emplacement à Déterminer", "orderSummary": "R<PERSON><PERSON><PERSON>", "subtotal": "Sous-total", "shipping": "<PERSON><PERSON><PERSON>", "arouzFee": "Frais AROUZ", "total": "Total", "termsText": "J'accepte les", "termsLink": "Conditions Générales", "and": "et", "privacyLink": "Politique de Confidentialité", "warningTitle": "<PERSON><PERSON>", "warningText": "Veuillez vous assurer que toutes les informations sont correctes avant de confirmer votre commande.", "processing": "Traitement en cours...", "confirmOrder": "Confirm<PERSON> la Commande"}}, "orderSuccess": {"title": "Commande Confirmée!", "description": "Merci pour votre commande. Nous la traiterons sous peu.", "orderNotFound": "Commande Non Trouvée", "orderDetails": "<PERSON><PERSON><PERSON> de la Commande", "orderNumber": "Numéro de Commande", "orderDate": "Date de Commande", "status": "Statut", "paymentMethod": "Méthode de Paiement", "customerInfo": "Informations Client", "name": "Nom", "phone": "Téléphone", "deliveryInfo": "<PERSON><PERSON> de Livraison", "deliveryAddress": "<PERSON><PERSON><PERSON>", "wilaya": "<PERSON><PERSON><PERSON>", "specialInstructions": "Instructions Spéciales", "viewOnMaps": "Voir sur les Cartes", "shippingOrigins": "Origines d'Expédition", "orderItems": "Articles de la Commande", "quantity": "Quantité", "supplier": "Fournisseur", "subtotal": "Sous-total", "shipping": "<PERSON><PERSON><PERSON>", "arouzFee": "Frais AROUZ", "total": "Total", "nextSteps": "Que Se Passe-t-il Ensuite?", "step1": "Nous confirmerons votre commande et l'assignerons à une société de livraison", "step2": "Vous recevrez les informations de suivi par SMS", "step3": "Votre commande sera livrée dans les 24-48 heures", "backToHome": "Retour à l'Accueil", "viewOrders": "Voir Mes Commandes"}, "orderStatus": {"pending": "En Attente", "confirmed": "Confirmée", "shipped": "Expédiée", "delivered": "Livrée", "cancelled": "<PERSON><PERSON><PERSON>"}, "validation": {"ratingRequired": "Évaluation Requise", "ratingRequiredDescription": "Veuillez sélectionner une évaluation par étoiles pour votre avis", "reviewContentRequired": "Contenu de l'Avis Requis", "reviewContentRequiredDescription": "Veuillez fournir un titre ou un texte d'avis", "reviewSubmissionFailed": "Échec de la Soumission de l'Avis", "reviewSubmissionFailedDescription": "<PERSON><PERSON><PERSON>z réessayer"}, "footer": {"trackYourOrder": "Suivre Votre Commande", "shop": "BOUTIQUE", "products": "PRODUITS", "help": "AIDE", "aboutUs": "À PROPOS", "services": "SERVICES", "basket": "<PERSON><PERSON>", "tyres": "Pneus", "oilsFluids": "<PERSON>les et Fluides", "batteries": "Batteries", "filters": "Filtres", "trackMyOrder": "<PERSON><PERSON><PERSON>", "contactUs": "<PERSON><PERSON>", "myAccount": "Mon Compte", "faq": "FAQ", "aboutArouz": "À Propos d'AROUZ", "careers": "Carrières", "investorRelations": "Relations Investisseurs", "corporateResponsibility": "Responsabilité d'Entreprise", "deliveryInfo": "<PERSON><PERSON> de Livraison", "returnPolicy": "Politique de Retour", "warranty": "<PERSON><PERSON><PERSON>", "installationServices": "Services d'Installation", "followUs": "Suivez-nous", "privacyPolicy": "Politique de Confidentialité", "termsOfService": "Conditions d'Utilisation", "sitemap": "Plan du Site", "poweredBy": "Propulsé par"}}