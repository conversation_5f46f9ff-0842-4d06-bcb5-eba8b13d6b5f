import React, { useState, useRef, useEffect, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Search, X, Car, Clock, TrendingUp } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';
import { useClickAway } from 'react-use';
import { useIsMobile } from '@/hooks/use-mobile';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { intelligentSearchService, SearchSuggestion } from '@/services/intelligentSearchService';
import { debounce } from 'lodash';

interface AirbnbStyleSearchBarProps {
  onSearch: (params: any) => void;
  initialValue?: string;
}

export function AirbnbStyleSearchBar({ onSearch, initialValue }: AirbnbStyleSearchBarProps) {
  const { t } = useTranslation();
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const { toast } = useToast();

  // Search state
  const [searchText, setSearchText] = useState(initialValue || '');
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [mobileSearchExpanded, setMobileSearchExpanded] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [popularTerms, setPopularTerms] = useState<string[]>([]);

  const searchBarRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Load popular terms and recent searches on mount
  useEffect(() => {
    loadPopularTerms();
    loadRecentSearches();
  }, []);

  // Debounced search suggestions
  const debouncedGetSuggestions = useCallback(
    debounce(async (query: string) => {
      if (query.length < 2) {
        setSuggestions([]);
        return;
      }

      setIsLoading(true);
      try {
        const searchSuggestions = await intelligentSearchService.getSuggestions(query, 8);
        setSuggestions(searchSuggestions);
      } catch (error) {
        console.error('Error getting suggestions:', error);
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    }, 300),
    []
  );

  // Handle search text change
  const handleSearchTextChange = (value: string) => {
    setSearchText(value);
    setShowSuggestions(true);
    debouncedGetSuggestions(value);
  };

  // Detect if text contains Arabic characters for RTL support
  const isRTL = (text: string) => {
    return /[\u0600-\u06FF]/.test(text);
  };

  // Get text direction based on content
  const getTextDirection = (text: string) => {
    return isRTL(text) ? 'rtl' : 'ltr';
  };

  // Handle search submission
  const handleSearch = (query?: string) => {
    const searchQuery = query || searchText;
    if (!searchQuery.trim()) return;

    // Save to recent searches
    saveToRecentSearches(searchQuery);

    // Removed vehicle info requirement - users can search for any product immediately

    // Navigate to search results page with proper browser history support
    navigate(`/search-results?q=${encodeURIComponent(searchQuery)}`, { replace: false });

    // Close suggestions and mobile search
    setShowSuggestions(false);
    if (isMobile) {
      setMobileSearchExpanded(false);
    }
  };

  // Load popular search terms
  const loadPopularTerms = async () => {
    try {
      const terms = await intelligentSearchService.getPopularSearchTerms(5);
      setPopularTerms(terms);
    } catch (error) {
      console.error('Error loading popular terms:', error);
    }
  };

  // Load recent searches from localStorage
  const loadRecentSearches = () => {
    try {
      const recent = localStorage.getItem('recentSearches');
      if (recent) {
        setRecentSearches(JSON.parse(recent).slice(0, 5));
      }
    } catch (error) {
      console.error('Error loading recent searches:', error);
    }
  };

  // Save search to recent searches
  const saveToRecentSearches = (query: string) => {
    try {
      const recent = [...recentSearches.filter(s => s !== query), query].slice(-5);
      setRecentSearches(recent);
      localStorage.setItem('recentSearches', JSON.stringify(recent));
    } catch (error) {
      console.error('Error saving recent search:', error);
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion: string) => {
    setSearchText(suggestion);
    handleSearch(suggestion);
  };

  // Handle mobile search toggle
  const handleMobileSearchToggle = () => {
    setMobileSearchExpanded(!mobileSearchExpanded);
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
    }
  };

  // Close suggestions when clicking away
  useClickAway(searchBarRef, () => {
    setShowSuggestions(false);
    if (mobileSearchExpanded) {
      setMobileSearchExpanded(false);
    }
  });

  return (
    <div className="relative z-50 w-full" ref={searchBarRef}>
      {/* Main search bar container */}
      <div className="mx-auto w-full">
        {/* Desktop Search Bar */}
        {!isMobile && (
          <div className="relative">
            <div
              className="bg-white rounded-full shadow-md border border-gray-200 transition-all duration-300 hover:shadow-lg"
            >
              {/* Search bar */}
              <div className="flex items-center h-16 px-6">
                {/* Search input */}
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    ref={inputRef}
                    type="text"
                    value={searchText}
                    onChange={(e) => handleSearchTextChange(e.target.value)}
                    onKeyPress={handleKeyPress}
                    onFocus={() => setShowSuggestions(true)}
                    placeholder={t('marketplace.searchAllProducts')}
                    dir={getTextDirection(searchText)}
                    className={`w-full p-3 text-base border-0 focus:outline-none focus:ring-0 bg-transparent placeholder-gray-500 ${
                      isRTL(searchText) ? 'pr-10 pl-12 text-right' : 'pl-10 pr-12 text-left'
                    }`}
                  />
                  {searchText && (
                    <button
                      onClick={() => {
                        setSearchText('');
                        setSuggestions([]);
                        setShowSuggestions(false);
                      }}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  )}
                </div>

                {/* Search button */}
                <div className="ml-4">
                  <button
                    onClick={() => handleSearch()}
                    disabled={!searchText.trim()}
                    className="bg-[#DC2626] hover:bg-[#B91C1C] disabled:bg-gray-300 text-white p-3 rounded-full transition-colors duration-200 flex items-center justify-center"
                  >
                    <Search className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>

            {/* Search Suggestions Dropdown */}
            <AnimatePresence>
              {showSuggestions && (searchText.length >= 2 || suggestions.length > 0 || recentSearches.length > 0 || popularTerms.length > 0) && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-lg border border-gray-200 z-50 max-h-96 overflow-y-auto"
                >
                  {isLoading && (
                    <div className="p-4 text-center text-gray-500">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#DC2626] mx-auto"></div>
                      <p className="mt-2 text-sm">{t('marketplace.searching')}</p>
                    </div>
                  )}

                  {!isLoading && suggestions.length > 0 && (
                    <div className="p-2">
                      <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide">
                        {t('marketplace.suggestions')}
                      </div>
                      {suggestions.map((suggestion, index) => (
                        <button
                          key={index}
                          onClick={() => handleSuggestionClick(suggestion.text)}
                          className="w-full text-left px-3 py-2 hover:bg-gray-50 rounded-lg flex items-center justify-between group"
                        >
                          <div className="flex items-center">
                            <Search className="h-4 w-4 text-gray-400 mr-3" />
                            <span className="text-gray-900">{suggestion.text}</span>
                          </div>
                          <span className="text-xs text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity">
                            {suggestion.type}
                          </span>
                        </button>
                      ))}
                    </div>
                  )}

                  {!isLoading && searchText.length < 2 && recentSearches.length > 0 && (
                    <div className="p-2 border-t border-gray-100">
                      <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {t('marketplace.recentSearches')}
                      </div>
                      {recentSearches.map((search, index) => (
                        <button
                          key={index}
                          onClick={() => handleSuggestionClick(search)}
                          className="w-full text-left px-3 py-2 hover:bg-gray-50 rounded-lg flex items-center"
                        >
                          <Clock className="h-4 w-4 text-gray-400 mr-3" />
                          <span className="text-gray-700">{search}</span>
                        </button>
                      ))}
                    </div>
                  )}

                  {!isLoading && searchText.length < 2 && popularTerms.length > 0 && (
                    <div className="p-2 border-t border-gray-100">
                      <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide flex items-center">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        {t('marketplace.popularSearches')}
                      </div>
                      {popularTerms.map((term, index) => (
                        <button
                          key={index}
                          onClick={() => handleSuggestionClick(term)}
                          className="w-full text-left px-3 py-2 hover:bg-gray-50 rounded-lg flex items-center"
                        >
                          <TrendingUp className="h-4 w-4 text-gray-400 mr-3" />
                          <span className="text-gray-700">{term}</span>
                        </button>
                      ))}
                    </div>
                  )}

                  {!isLoading && searchText.length >= 2 && suggestions.length === 0 && (
                    <div className="p-4 text-center text-gray-500">
                      <p className="text-sm">{t('marketplace.noSuggestions')}</p>
                    </div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        )}

        {/* Mobile Search Bar */}
        {isMobile && (
          <div className="bg-white rounded-full shadow-md border border-gray-200">
            {/* Collapsed mobile search bar */}
            <div
              className="flex items-center h-12 px-4 cursor-pointer"
              onClick={handleMobileSearchToggle}
            >
              <Search className="w-4 h-4 text-gray-500 mr-2" />
              <div className="flex-1 text-sm text-gray-500">
                {searchText || t('marketplace.startYourSearch')}
              </div>
            </div>
          </div>
        )}

        {/* Mobile Expanded Search Interface */}
        <AnimatePresence>
          {isMobile && mobileSearchExpanded && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
              className="fixed inset-0 bg-white z-50 overflow-y-auto"
            >
              {/* Mobile search header */}
              <div className="flex items-center justify-between p-4 border-b">
                <button
                  className="text-gray-500"
                  onClick={handleMobileSearchToggle}
                >
                  <X className="h-6 w-6" />
                </button>
                <div className="text-base font-medium">
                  {t('marketplace.search')}
                </div>
                <div className="w-6"></div> {/* Empty div for alignment */}
              </div>

              {/* Mobile search content */}
              <div className="p-4 space-y-4">
                {/* Text search input */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">{t('marketplace.searchText')}</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                      type="text"
                      value={searchText}
                      onChange={(e) => handleSearchTextChange(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder={t('marketplace.searchAllProducts')}
                      dir={getTextDirection(searchText)}
                      className={`w-full p-3 border border-gray-300 rounded-lg focus:ring-[#DC2626] focus:border-[#DC2626] ${
                        isRTL(searchText) ? 'pr-10 text-right' : 'pl-10 text-left'
                      }`}
                    />
                  </div>
                </div>

                {/* Mobile suggestions */}
                {suggestions.length > 0 && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">{t('marketplace.suggestions')}</label>
                    <div className="space-y-1">
                      {suggestions.map((suggestion, index) => (
                        <button
                          key={index}
                          onClick={() => handleSuggestionClick(suggestion.text)}
                          className="w-full text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 flex items-center"
                        >
                          <Search className="h-4 w-4 text-gray-400 mr-3" />
                          <span className="text-gray-900">{suggestion.text}</span>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Recent searches for mobile */}
                {searchText.length < 2 && recentSearches.length > 0 && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700 flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {t('marketplace.recentSearches')}
                    </label>
                    <div className="space-y-1">
                      {recentSearches.map((search, index) => (
                        <button
                          key={index}
                          onClick={() => handleSuggestionClick(search)}
                          className="w-full text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 flex items-center"
                        >
                          <Clock className="h-4 w-4 text-gray-400 mr-3" />
                          <span className="text-gray-700">{search}</span>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Popular terms for mobile */}
                {searchText.length < 2 && popularTerms.length > 0 && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700 flex items-center">
                      <TrendingUp className="h-4 w-4 mr-1" />
                      {t('marketplace.popularSearches')}
                    </label>
                    <div className="space-y-1">
                      {popularTerms.map((term, index) => (
                        <button
                          key={index}
                          onClick={() => handleSuggestionClick(term)}
                          className="w-full text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 flex items-center"
                        >
                          <TrendingUp className="h-4 w-4 text-gray-400 mr-3" />
                          <span className="text-gray-700">{term}</span>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Search button */}
                <div className="pt-4">
                  <button
                    onClick={() => handleSearch()}
                    disabled={!searchText.trim()}
                    className="w-full bg-[#DC2626] hover:bg-[#B91C1C] disabled:bg-gray-300 text-white py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center"
                  >
                    <Search className="w-5 h-5 mr-2" />
                    {t('marketplace.search')}
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}