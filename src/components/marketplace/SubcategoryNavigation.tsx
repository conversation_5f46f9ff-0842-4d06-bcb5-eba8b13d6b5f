import React, { useRef, useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { FileText, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { getSubcategoriesByCategory } from '@/data/categoryData';
import { getFallbackImageUrl } from '@/services/categoryImageService';
import { useLegacyCategories, useMigrationStatus } from '@/data/categoryDataSupabase';
import { useSubcategoryImageUrl } from '@/hooks/useCategoryImage';
import { useMarketplaceSubcategoryNavigation } from '@/hooks/useTranslatedCategories';

// Subcategory data is now centralized in @/data/categoryData

// Subcategory card component that uses the image hook
interface SubcategoryCardProps {
  subcategory: {
    id: string;
    name: string;
    displayName: string;
  };
  isSelected: boolean;
  shouldWrap: boolean;
  onClick: () => void;
}

const SubcategoryCard: React.FC<SubcategoryCardProps> = ({
  subcategory,
  isSelected,
  shouldWrap,
  onClick
}) => {
  const { imageUrl, isLoading } = useSubcategoryImageUrl(subcategory.id);

  return (
    <div className="flex flex-col items-center min-w-[80px] md:min-w-[100px]">
      <button
        className={cn(
          "flex flex-col items-center gap-1 md:gap-2 py-1 md:py-2 px-2 md:px-3 transition-all duration-75 min-w-[75px] md:min-w-[95px] group",
          "hover:scale-105 active:scale-95"
        )}
        onClick={onClick}
      >
        {/* Image container - Clean white circular background with selection ring only */}
        <div className={cn(
          "w-16 h-16 md:w-20 md:h-20 rounded-full bg-white flex items-center justify-center overflow-hidden transition-all duration-75",
          isSelected
            ? "ring-2 ring-[#DC2626] ring-offset-1 md:ring-offset-2 shadow-sm"
            : "hover:shadow-md"
        )}>
          {isLoading ? (
            // Loading skeleton
            <div className="w-12 h-12 md:w-16 md:h-16 bg-gray-200 animate-pulse rounded-full" />
          ) : (
            <img
              src={imageUrl}
              alt={subcategory.displayName}
              className="w-12 h-12 md:w-16 md:h-16 object-contain"
              onError={(e) => {
                // Fallback to placeholder on error
                const target = e.target as HTMLImageElement;
                target.src = getFallbackImageUrl('subcategory');
              }}
            />
          )}
        </div>

        {/* Subcategory name - Smart text wrapping */}
        <div className={cn(
          "text-center transition-colors duration-75 flex items-center justify-center",
          shouldWrap ? "h-6 md:h-8 leading-tight" : "h-4 md:h-5",
          isSelected
            ? "text-gray-900 font-semibold"
            : "text-gray-700 group-hover:text-gray-900"
        )}>
          <span className={cn(
            "font-medium",
            shouldWrap
              ? "text-[10px] md:text-[11px] leading-3 max-w-[75px] md:max-w-[100px]"
              : "text-[11px] md:text-sm whitespace-nowrap"
          )}>
            {subcategory.displayName}
          </span>
        </div>
      </button>


    </div>
  );
};

interface SubcategoryNavigationProps {
  selectedCategory: string;
  onSubcategorySelect: (subcategory: string) => void;
  selectedSubcategory: string;
}

export function SubcategoryNavigation({
  selectedCategory,
  onSubcategorySelect,
  selectedSubcategory
}: SubcategoryNavigationProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(false);

  // Get translated subcategories for the selected category
  const translatedSubcategories = useMarketplaceSubcategoryNavigation(selectedCategory);

  // Use Supabase categories with localStorage fallback as backup
  const { categories: supabaseCategories } = useLegacyCategories();
  const { isMigrated } = useMigrationStatus();

  // Get subcategories - prioritize translated subcategories, fallback to legacy system
  const getSubcategories = () => {
    // If we have translated subcategories, use them
    if (translatedSubcategories.length > 0) {
      return translatedSubcategories;
    }

    // Fallback to legacy system
    if (isMigrated) {
      // Use Supabase data
      const category = supabaseCategories.find(cat => cat.id === selectedCategory);
      return category?.subcategories || [];
    } else {
      // Fallback to localStorage data
      return getSubcategoriesByCategory(selectedCategory);
    }
  };

  const subcategories = getSubcategories();

  // Check scroll position to show/hide arrows
  const checkScroll = () => {
    if (!scrollContainerRef.current) return;

    const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
    setShowLeftArrow(scrollLeft > 0);
    setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10); // 10px buffer
  };

  // Scroll left/right
  const scroll = (direction: 'left' | 'right') => {
    if (!scrollContainerRef.current) return;

    const scrollAmount = 300; // Adjust as needed
    const newScrollLeft = direction === 'left'
      ? scrollContainerRef.current.scrollLeft - scrollAmount
      : scrollContainerRef.current.scrollLeft + scrollAmount;

    scrollContainerRef.current.scrollTo({
      left: newScrollLeft,
      behavior: 'smooth'
    });
  };

  // Listen for scroll events
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', checkScroll);
      // Initial check
      checkScroll();

      return () => scrollContainer.removeEventListener('scroll', checkScroll);
    }
  }, []);

  // Check scroll when window resizes
  useEffect(() => {
    window.addEventListener('resize', checkScroll);
    return () => window.removeEventListener('resize', checkScroll);
  }, []);

  // Don't render if no category is selected or no subcategories available
  if (!selectedCategory || subcategories.length === 0) {
    return null;
  }

  return (
    <div className="mb-4">
      {/* Airbnb-style Horizontal Scrollable Subcategories */}
      <div className="relative">
        {/* Left scroll arrow */}
        {showLeftArrow && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute left-0 top-1/2 -translate-y-1/2 z-10 h-8 w-8 bg-white/90 shadow-md hover:bg-white rounded-full border border-gray-200 hidden sm:flex"
            onClick={() => scroll('left')}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        )}

        {/* Scrollable subcategories container */}
        <div
          ref={scrollContainerRef}
          className="flex items-center gap-3 md:gap-4 overflow-x-auto scrollbar-hide px-2 sm:px-10"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {subcategories.map((subcategory) => {
            // Smart text wrapping logic for subcategories - based on character count (18+ chars including spaces)
            const shouldWrap = subcategory.displayName.length >= 18;

            return (
              <SubcategoryCard
                key={subcategory.id}
                subcategory={subcategory}
                isSelected={selectedSubcategory === subcategory.name}
                shouldWrap={shouldWrap}
                onClick={() => onSubcategorySelect(subcategory.name)}
              />
            );
          })}
        </div>

        {/* Right scroll arrow */}
        {showRightArrow && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-0 top-1/2 -translate-y-1/2 z-10 h-8 w-8 bg-white/90 shadow-md hover:bg-white rounded-full border border-gray-200 hidden sm:flex"
            onClick={() => scroll('right')}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}
